# 背景：
- 前端 Vue 样式风格项目（demo）目录： `art-design-pro/art-design-pro-src`；
- 完整的线上运行的生产项目（pord）目录：`src`；


# 任务：
- 参考 demo 项目升级(替换) pord 项目UI，确保升级后 pord 项目的风格、布局、UI组件等视觉上跟 demo 保持一致；
- 直接修改 pord 项目的源代码，但业务逻辑需要和原始代码保持一致（保留除UI外的任何东西）；
- 新UI同样需要适配电脑端和手机端页面；
- 开发过程中，如果要用到 npm 工具那么使用 pnpm 代替；



现在仍然存在几个问题：
- 切换亮色/暗色模式后，左则菜单仍然有问题（菜单大部份区域仍然是亮色模式）。
- 网页初始状态下（打开或刷新后），左则边栏与右则内容区衔接的地方有少许错位，sidebar-toggle 后恢复正常。


- 在右则header中，refresh-btn左则增加一个缩小/展开侧边栏的按钮（现在已有相同功能：sidebar-toggle）。
- 左侧菜单栏缩小后，右则内容区没有对齐（没有占满可用空间）。



创建一个新UI组件（el-drawerdialog），要求实现：
- 新组件参数要跟 el-drawer 兼容;
- 替代 el-drawer 功能，实现在电脑端使用居中 el-dialog 模式显示，在手机端使用 el-drawer 模式显示；
- 【确定】/【取消】按钮出现在底部位置（在 el-drawerdialog 中指定）；
- el-dialog 显示模式下，表格（el-form）使用左右结构；




创建一个新页面及对应的后端API：dashboard，作用为每个登录角色(用户)展示系统概况，页面中以图表/Card方式展示。


后端要求：
- 先定义出所有需要展示的项目，并为每个项目定义出ID；
- 再定义每个角色可以查看的项目ID；
- 需要展示的项目通过json列表返回，每项一个图表/Card；
- 不要使用mock或硬编码数据，而是通过接口、数据库中统计等得到；

前端要求：
- 通过服务器端返回的json列表展示；
- 电脑端每行最多放置4个图表/Card（由后端通过json控制占位和类型）；
- UI要适合电脑端及手机端；
- 图表组件使用 `demo` 项目中已经定义好的组件（必要时添加依赖）；

角色对应汇总图表/Card：
- 系统管理员：日活，周活，月活，总用户数，新增用户数，活跃用户数，用户增长趋势，及所有项目；
- 广告主：投放中的广告数量，广告展示数，广告点击数，余额，日消费，周消费，月消费；
- APP开发者：广告展示数，广告点击数，日收益，周收益，月收益，日活，周活，月活，总用户数；




（日活，周活，月活，总用户数，新增用户数，活跃用户数，用户增长趋势，用户留存率，用户流失率，用户生命周期，用户生命周期价值，用户生命周期成本，用户生命周期ROI，用户生命周期ROI趋势，用户生命周期ROI预测，用户生命周期ROI预测趋势，用户生命周期ROI预测预测，用户生命周期ROI预测预测趋势，用户生命周期ROI预测预测预测，用户生命周期ROI预测预测预测趋势，用户生命周期ROI预测预测预测预测）

- 使用echarts 展示数据；
- 展示的数据包括：CPU使用率、内存使用率、硬盘使用率、网络使用率；
- 展示的数据包括：CPU温度、内存温度、硬盘温度、网络温度；
- 展示的数据包括：CPU风扇转速、内存风扇转速、硬盘风扇转速、网络风扇转速；
- 展示的数据包括：CPU电压、内存电压、硬盘电压、网络电压；






为项目开发一个使用用加密货币进行充值的页面Deposit（单页面即可），
如需要参数 https://www.cryptorefills.com/zh





