<template>
  <div class="crypto-recharge-container">
    <!-- 页面头部 -->
    <!-- 主要内容区域 -->
    <div class="main-content">
      <el-row :gutter="24">
        <!-- 左侧：充值表单 -->
        <el-col :xs="24" :lg="16">
          <div class="recharge-form-section">
            <!-- 加密货币选择 -->
            <!-- <div class="crypto-selection">
              <h3 class="section-title">选择加密货币</h3>
              <div class="crypto-cards">
                <CryptoPriceCard
                  v-for="crypto in cryptoList"
                  :key="crypto.symbol"
                  :crypto="crypto"
                  :is-selected="selectedCrypto?.symbol === crypto.symbol"
                  @select="selectCrypto"
                />
              </div>
            </div> -->

            <!-- 充值金额输入 -->
            <div class="amount-input-section" v-if="selectedCrypto">
              <h3 class="section-title">充值金额</h3>
              <el-card class="amount-card" shadow="never">
                <div class="amount-input-group">
                  <div class="input-row">
                    <label class="input-label">充值金额 (USD)</label>
                    <el-input
                      v-model="rechargeAmount"
                      type="number"
                      placeholder="请输入充值金额"
                      size="large"
                      @input="calculateCryptoAmount"
                    >
                      <template #prefix>¥</template>
                    </el-input>
                  </div>
                  <div class="conversion-arrow">
                    <el-icon><ArrowDown /></el-icon>
                  </div>
                  <div class="input-row">
                    <label class="input-label">获得 {{ selectedCrypto.symbol }}</label>
                    <el-input
                      v-model="cryptoAmount"
                      readonly
                      size="large"
                    >
                      <template #suffix>{{ selectedCrypto.symbol }}</template>
                    </el-input>
                  </div>
                </div>
                
                <!-- 快捷金额选择 -->
                <div class="quick-amounts">
                  <span class="quick-label">快捷选择：</span>
                  <el-button 
                    v-for="amount in quickAmounts" 
                    :key="amount"
                    size="small"
                    @click="setQuickAmount(amount)"
                  >
                    ¥{{ amount.toLocaleString() }}
                  </el-button>
                </div>
              </el-card>
            </div>
          </div>
        </el-col>

        <!-- 右侧：支付信息 -->
        <el-col :xs="24" :lg="8">
          <div class="payment-info-section" v-if="selectedCrypto && rechargeAmount">
            <el-card class="payment-card" shadow="never">
              <template #header>
                <div class="card-header">
                  <h3>支付信息</h3>
                  <el-tag type="warning" size="small">请在30分钟内完成支付</el-tag>
                </div>
              </template>

              <!-- 支付金额信息 -->
              <div class="payment-summary">
                <div class="summary-row">
                  <span class="label">充值金额：</span>
                  <span class="value">¥{{ Number(rechargeAmount).toLocaleString() }}</span>
                </div>
                <div class="summary-row">
                  <span class="label">获得数量：</span>
                  <span class="value">{{ cryptoAmount }} {{ selectedCrypto.symbol }}</span>
                </div>
                <div class="summary-row">
                  <span class="label">汇率：</span>
                  <span class="value">1 {{ selectedCrypto.symbol }} = ¥{{ selectedCrypto.price.toLocaleString() }}</span>
                </div>
              </div>

              <el-divider />

              <!-- 钱包地址 -->
              <div class="wallet-address">
                <div class="address-label">
                  <el-icon><Wallet /></el-icon>
                  <span>{{ selectedCrypto.name }} 钱包地址</span>
                </div>
                <div class="address-content">
                  <el-input
                    v-model="walletAddress"
                    readonly
                    size="small"
                  >
                    <template #append>
                      <el-button @click="copyAddress" size="small">
                        <el-icon><CopyDocument /></el-icon>
                      </el-button>
                    </template>
                  </el-input>
                </div>
              </div>

              <!-- 二维码 -->
              <div class="qr-code-section">
                <QRCodeGenerator :text="walletAddress" :size="160" />
                <p class="qr-tip">扫描二维码或复制地址进行转账</p>
              </div>

              <!-- 支付说明 -->
              <div class="payment-instructions">
                <h4>支付说明</h4>
                <ul>
                  <li>请确保转账网络为 {{ selectedCrypto.network }}</li>
                  <li>最小充值金额：{{ selectedCrypto.minAmount }} {{ selectedCrypto.symbol }}</li>
                  <li>到账时间：{{ selectedCrypto.confirmations }} 个网络确认</li>
                  <li>充值完成后，资金将自动到账</li>
                </ul>
              </div>

              <!-- 确认充值按钮 -->
              <div class="action-buttons">
                <el-button type="primary" size="large" block @click="confirmRecharge">
                  我已完成转账
                </el-button>
              </div>
            </el-card>
          </div>
        </el-col>
      </el-row>

      <!-- 充值记录 -->
      <div class="recharge-history" v-if="showHistory">
        <el-card class="history-card" shadow="never">
          <template #header>
            <div class="card-header">
              <h3>充值记录</h3>
              <el-button size="small" @click="refreshHistory">
                <el-icon><Refresh /></el-icon>
                刷新
              </el-button>
            </div>
          </template>

          <el-table :data="rechargeHistory" style="width: 100%">
            <el-table-column prop="time" label="时间" width="180" />
            <el-table-column prop="crypto" label="币种" width="100" />
            <el-table-column prop="amount" label="数量" />
            <el-table-column prop="value" label="价值" />
            <el-table-column prop="status" label="状态" width="120">
              <template #default="scope">
                <el-tag 
                  :type="getStatusType(scope.row.status)"
                  size="small"
                >
                  {{ scope.row.status }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="txHash" label="交易哈希" width="200">
              <template #default="scope">
                <el-button 
                  link 
                  type="primary" 
                  size="small"
                  @click="viewTransaction(scope.row.txHash)"
                >
                  {{ scope.row.txHash.substring(0, 10) }}...
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import {
  ArrowDown,
  Wallet,
  CopyDocument,
  Picture,
  Refresh
} from '@element-plus/icons-vue'
import QRCodeGenerator from './components/QRCodeGenerator.vue'
import CryptoPriceCard from './components/CryptoPriceCard.vue'

defineOptions({
  name: 'CryptoRecharge'
})

// 响应式数据
const selectedCrypto = ref(null)
const rechargeAmount = ref('')
const cryptoAmount = ref('')
const walletAddress = ref('')
const showHistory = ref(true)

// 加密货币列表
const cryptoList = ref([
  {
    symbol: 'BTC',
    name: 'Bitcoin',
    icon: '/src/assets/icons/crypto/btc.svg',
    price: 680000,
    change: 2.5,
    network: 'Bitcoin',
    minAmount: 0.001,
    confirmations: 3
  },
  {
    symbol: 'ETH',
    name: 'Ethereum',
    icon: '/src/assets/icons/crypto/eth.svg',
    price: 24000,
    change: -1.2,
    network: 'Ethereum (ERC-20)',
    minAmount: 0.01,
    confirmations: 12
  },
  {
    symbol: 'USDT',
    name: 'Tether',
    icon: '/src/assets/icons/crypto/usdt.svg',
    price: 7.2,
    change: 0.1,
    network: 'Ethereum (ERC-20)',
    minAmount: 10,
    confirmations: 12
  }
])

// 快捷金额
const quickAmounts = ref([100, 500, 1000, 5000, 10000])

// 充值记录
const rechargeHistory = ref([
  {
    time: '2024-12-20 14:30:25',
    crypto: 'BTC',
    amount: '0.001',
    value: '¥680',
    status: '已完成',
    txHash: '******************************************'
  },
  {
    time: '2024-12-19 10:15:30',
    crypto: 'ETH',
    amount: '0.5',
    value: '¥12,000',
    status: '确认中',
    txHash: '******************************************'
  }
])

// 方法
const selectCrypto = (crypto) => {
  selectedCrypto.value = crypto
  generateWalletAddress()
  calculateCryptoAmount()
}

const generateWalletAddress = () => {
  // 模拟生成钱包地址
  const addresses = {
    BTC: '**********************************',
    ETH: '******************************************',
    USDT: '******************************************'
  }
  walletAddress.value = addresses[selectedCrypto.value.symbol] || ''
}

const calculateCryptoAmount = () => {
  if (rechargeAmount.value && selectedCrypto.value) {
    const amount = Number(rechargeAmount.value) / selectedCrypto.value.price
    cryptoAmount.value = amount.toFixed(8)
  } else {
    cryptoAmount.value = ''
  }
}

const setQuickAmount = (amount) => {
  rechargeAmount.value = amount.toString()
  calculateCryptoAmount()
}

const copyAddress = async () => {
  try {
    await navigator.clipboard.writeText(walletAddress.value)
    ElMessage.success('钱包地址已复制到剪贴板')
  } catch (err) {
    ElMessage.error('复制失败，请手动复制')
  }
}

const confirmRecharge = () => {
  ElMessage.success('充值确认已提交，请等待网络确认')
}

const refreshHistory = () => {
  ElMessage.success('充值记录已刷新')
}

const getStatusType = (status) => {
  const statusMap = {
    '已完成': 'success',
    '确认中': 'warning',
    '失败': 'danger'
  }
  return statusMap[status] || 'info'
}

const viewTransaction = (txHash) => {
  ElMessage.info(`查看交易：${txHash}`)
}

onMounted(() => {
  // 默认选择第一个加密货币
  if (cryptoList.value.length > 0) {
    selectCrypto(cryptoList.value[2])
  }
})
</script>

<style lang="scss" scoped>
.crypto-recharge-container {
  min-height: 100vh;
  background: var(--art-bg-color);
  padding: 20px;

  .page-header {
    background: var(--art-main-bg-color);
    border-radius: var(--art-border-radius-lg);
    padding: 40px 0;
    margin-bottom: 24px;
    text-align: center;
    box-shadow: var(--art-box-shadow-sm);
    border: 1px solid var(--art-border-color);

    .header-content {
      max-width: 600px;
      margin: 0 auto;

      .page-title {
        font-size: 2.5rem;
        font-weight: 600;
        color: var(--art-text-gray-900);
        margin-bottom: 12px;
        background: linear-gradient(135deg, rgb(var(--art-primary)), rgb(var(--art-secondary)));
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
      }

      .page-subtitle {
        font-size: 1.1rem;
        color: var(--art-text-gray-600);
        margin: 0;
      }
    }
  }

  .main-content {
    max-width: 1400px;
    margin: 0 auto;
  }

  .recharge-form-section {
    .section-title {
      font-size: 1.25rem;
      font-weight: 600;
      color: var(--art-text-gray-900);
      margin-bottom: 16px;
      display: flex;
      align-items: center;

      &::before {
        content: '';
        width: 4px;
        height: 20px;
        background: linear-gradient(135deg, rgb(var(--art-primary)), rgb(var(--art-secondary)));
        border-radius: 2px;
        margin-right: 12px;
      }
    }
  }

  .crypto-selection {
    margin-bottom: 32px;

    .crypto-cards {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
      gap: 16px;
    }
  }

  .amount-input-section {
    .amount-card {
      border: 1px solid var(--art-border-color);
      box-shadow: var(--art-box-shadow-sm);

      :deep(.el-card__body) {
        padding: 24px;
      }
    }

    .amount-input-group {
      .input-row {
        margin-bottom: 16px;

        .input-label {
          display: block;
          font-size: 0.9rem;
          font-weight: 500;
          color: var(--art-text-gray-700);
          margin-bottom: 8px;
        }

        :deep(.el-input) {
          .el-input__wrapper {
            border-radius: var(--art-border-radius);
            box-shadow: 0 0 0 1px var(--art-border-color);
            transition: all var(--art-transition-duration);

            &:hover {
              box-shadow: 0 0 0 1px rgb(var(--art-primary));
            }

            &.is-focus {
              box-shadow: 0 0 0 2px rgba(var(--art-primary), 0.2);
            }
          }
        }
      }

      .conversion-arrow {
        text-align: center;
        margin: 16px 0;
        color: var(--art-text-gray-500);

        .el-icon {
          font-size: 20px;
        }
      }
    }

    .quick-amounts {
      margin-top: 20px;
      padding-top: 20px;
      border-top: 1px solid var(--art-border-color);
      display: flex;
      align-items: center;
      gap: 12px;
      flex-wrap: wrap;

      .quick-label {
        font-size: 0.9rem;
        color: var(--art-text-gray-600);
        margin-right: 8px;
      }

      .el-button {
        border-radius: var(--art-border-radius);
        transition: all var(--art-transition-duration);

        &:hover {
          background: rgb(var(--art-primary));
          border-color: rgb(var(--art-primary));
          color: white;
        }
      }
    }
  }

  .payment-info-section {
    .payment-card {
      border: 1px solid var(--art-border-color);
      box-shadow: var(--art-box-shadow-sm);
      position: sticky;
      top: 20px;

      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;

        h3 {
          margin: 0;
          font-size: 1.1rem;
          font-weight: 600;
          color: var(--art-text-gray-900);
        }
      }

      :deep(.el-card__body) {
        padding: 24px;
      }
    }

    .payment-summary {
      .summary-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12px;

        &:last-child {
          margin-bottom: 0;
        }

        .label {
          font-size: 0.9rem;
          color: var(--art-text-gray-600);
        }

        .value {
          font-weight: 600;
          color: var(--art-text-gray-900);
        }
      }
    }

    .wallet-address {
      margin-bottom: 24px;

      .address-label {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 12px;
        font-size: 0.9rem;
        font-weight: 500;
        color: var(--art-text-gray-700);

        .el-icon {
          color: rgb(var(--art-primary));
        }
      }

      :deep(.el-input-group__append) {
        background: rgb(var(--art-primary));
        border-color: rgb(var(--art-primary));

        .el-button {
          background: transparent;
          border: none;
          color: white;

          &:hover {
            background: rgba(255, 255, 255, 0.1);
          }
        }
      }
    }

    .qr-code-section {
      text-align: center;
      margin-bottom: 24px;

      .qr-code {
        width: 160px;
        height: 160px;
        margin: 0 auto 12px;
        border: 2px dashed var(--art-border-color);
        border-radius: var(--art-border-radius);
        display: flex;
        align-items: center;
        justify-content: center;
        background: var(--art-gray-100);

        .qr-placeholder {
          text-align: center;
          color: var(--art-text-gray-500);

          .el-icon {
            margin-bottom: 8px;
          }

          p {
            margin: 0;
            font-size: 0.85rem;
          }
        }
      }

      .qr-tip {
        font-size: 0.85rem;
        color: var(--art-text-gray-600);
        margin: 0;
      }
    }

    .payment-instructions {
      margin-bottom: 24px;

      h4 {
        font-size: 1rem;
        font-weight: 600;
        color: var(--art-text-gray-900);
        margin-bottom: 12px;
      }

      ul {
        margin: 0;
        padding-left: 20px;

        li {
          font-size: 0.85rem;
          color: var(--art-text-gray-600);
          margin-bottom: 6px;
          line-height: 1.5;

          &:last-child {
            margin-bottom: 0;
          }
        }
      }
    }

    .action-buttons {
      .el-button {
        height: 48px;
        font-size: 1rem;
        font-weight: 600;
        border-radius: var(--art-border-radius);
        background: linear-gradient(135deg, rgb(var(--art-primary)), rgb(var(--art-secondary)));
        border: none;
        transition: all var(--art-transition-duration);

        &:hover {
          transform: translateY(-1px);
          box-shadow: var(--art-box-shadow);
        }
      }
    }
  }

  .recharge-history {
    margin-top: 32px;

    .history-card {
      border: 1px solid var(--art-border-color);
      box-shadow: var(--art-box-shadow-sm);

      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;

        h3 {
          margin: 0;
          font-size: 1.1rem;
          font-weight: 600;
          color: var(--art-text-gray-900);
        }

        .el-button {
          border-radius: var(--art-border-radius);
        }
      }

      :deep(.el-table) {
        .el-table__header {
          th {
            background: var(--art-gray-100);
            color: var(--art-text-gray-700);
            font-weight: 600;
          }
        }

        .el-table__row {
          &:hover {
            background: var(--art-gray-100);
          }
        }
      }
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    padding: 12px;

    .page-header {
      padding: 24px 20px;
      margin-bottom: 16px;

      .page-title {
        font-size: 2rem;
      }

      .page-subtitle {
        font-size: 1rem;
      }
    }

    .crypto-cards {
      grid-template-columns: 1fr;
    }

    .payment-info-section {
      margin-top: 24px;

      .payment-card {
        position: static;
      }
    }

    .quick-amounts {
      .el-button {
        flex: 1;
        min-width: 80px;
      }
    }
  }

  @media (max-width: 480px) {
    .quick-amounts {
      flex-direction: column;
      align-items: stretch;

      .quick-label {
        margin-bottom: 8px;
      }

      .el-button {
        width: 100%;
        margin-bottom: 8px;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}

// 暗色主题适配
:deep(.dark) {
  .qr-code {
    background: var(--art-gray-200) !important;
  }
}
</style>
