# USDT充值向导组件使用指南

这是一个完整的USDT充值向导页面组件，提供了从金额选择到完成确认的四步充值流程。

## 快速开始

### 1. 直接使用组件

```vue
<template>
  <div>
    <Deposit />
  </div>
</template>

<script setup>
import Deposit from '@/view/deposit/deposit.vue'
</script>
```

### 2. 添加路由

在 `src/router/index.js` 中添加：

```javascript
{
  path: '/deposit',
  name: 'Deposit',
  meta: {
    title: 'USDT充值',
    requiresAuth: true
  },
  component: () => import('@/view/deposit/deposit.vue')
}
```

### 3. 菜单配置

如果需要在侧边栏菜单中添加，可以在菜单配置中加入：

```javascript
{
  path: '/deposit',
  name: 'deposit',
  component: 'view/deposit/deposit.vue',
  meta: {
    title: 'USDT充值',
    icon: 'money',
    keepAlive: false
  }
}
```

## 组件特性

### 第一步：金额选择
- ✅ 自由输入USDT金额
- ✅ 快速选择按钮（10、50、100、200、500、1000 USDT）
- ✅ 最低金额验证（10 USDT）
- ✅ 实时输入验证
- ✅ 充值说明和费用信息

### 第二步：支付信息
- ✅ 显示充值详情（金额、网络、到账时间）
- ✅ TRC20收款地址展示
- ✅ 一键复制地址功能
- ✅ 自动生成二维码
- ✅ 重要提示和注意事项

### 第三步：凭证提交
- ✅ 交易哈希输入
- ✅ 支付截图上传（支持拖拽）
- ✅ 文件格式验证（JPG、PNG）
- ✅ 文件大小限制（10MB）
- ✅ 可选备注信息

### 第四步：完成确认
- ✅ 成功提示页面
- ✅ 订单信息展示
- ✅ 处理时间说明
- ✅ 后续操作引导

## API集成

### 必需的API接口

#### 1. 获取收款地址
```javascript
GET /api/wallet/address?currency=USDT&network=TRC20

// 响应格式
{
  "code": 200,
  "data": {
    "address": "TQn9Y2khEsLJW1ChVWFMSMeRDow5KcbLSE",
    "network": "TRC20",
    "currency": "USDT"
  }
}
```

#### 2. 提交充值申请
```javascript
POST /api/deposit/submit
Content-Type: multipart/form-data

// 请求参数
{
  "amount": "100",
  "walletAddress": "TQn9Y2khEsLJW1ChVWFMSMeRDow5KcbLSE",
  "transactionHash": "xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx",
  "network": "TRC20",
  "currency": "USDT",
  "screenshot": File,
  "remark": "备注信息"
}

// 响应格式
{
  "code": 200,
  "data": {
    "orderId": "12345",
    "orderNumber": "DP20241201001",
    "status": "pending",
    "submitTime": "2024-12-01 10:30:00"
  }
}
```

#### 3. 查询充值记录
```javascript
GET /api/deposit/history?page=1&pageSize=10

// 响应格式
{
  "code": 200,
  "data": {
    "list": [...],
    "total": 100,
    "page": 1,
    "pageSize": 10
  }
}
```

### API集成示例

修改 `deposit.vue` 中的相关方法：

```javascript
// 生成二维码时获取收款地址
const generateQRCode = async () => {
  try {
    // 调用API获取收款地址
    const response = await fetch('/api/wallet/address?currency=USDT&network=TRC20')
    const result = await response.json()
    
    if (result.code === 200) {
      walletAddress.value = result.data.address
      
      // 生成二维码
      await nextTick()
      if (qrCode.value) {
        const canvas = document.createElement('canvas')
        await QRCode.toCanvas(canvas, walletAddress.value, {
          width: 200,
          margin: 2
        })
        qrCode.value.innerHTML = ''
        qrCode.value.appendChild(canvas)
      }
    }
  } catch (error) {
    ElMessage.error('获取收款地址失败')
  }
}

// 提交凭证
const submitProof = async () => {
  if (!isProofValid.value) {
    ElMessage.error('请填写完整的支付凭证信息')
    return
  }

  submitting.value = true

  try {
    const formData = new FormData()
    formData.append('amount', depositAmount.value)
    formData.append('walletAddress', walletAddress.value)
    formData.append('transactionHash', transactionHash.value)
    formData.append('network', 'TRC20')
    formData.append('currency', 'USDT')
    formData.append('screenshot', uploadedFile.value.raw)
    if (remark.value) {
      formData.append('remark', remark.value)
    }

    const response = await fetch('/api/deposit/submit', {
      method: 'POST',
      body: formData,
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      }
    })

    const result = await response.json()
    
    if (result.code === 200) {
      orderNumber.value = result.data.orderNumber
      submitTime.value = result.data.submitTime
      nextStep()
      ElMessage.success('凭证提交成功')
    } else {
      ElMessage.error(result.message || '提交失败')
    }
  } catch (error) {
    ElMessage.error('提交失败，请重试')
  } finally {
    submitting.value = false
  }
}
```

## 自定义配置

### 修改快速金额选项

```javascript
// 在 deposit.vue 中修改
const quickAmounts = ref([20, 50, 100, 300, 500, 1000, 2000])
```

### 修改最低充值金额

```javascript
const validateAmount = () => {
  const amount = parseFloat(depositAmount.value)
  if (isNaN(amount) || amount <= 0) {
    amountError.value = '请输入有效金额'
  } else if (amount < 20) { // 修改最低金额
    amountError.value = '最低充值金额为20 USDT'
  } else {
    amountError.value = ''
  }
}
```

### 自定义样式

```scss
// 覆盖组件样式
.deposit-container {
  .step-card {
    border-radius: 12px; // 自定义圆角
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1); // 自定义阴影
  }
  
  .quick-buttons .el-button.selected {
    background-color: #your-brand-color; // 自定义主色调
  }
}
```

## 错误处理

### 常见错误处理

```javascript
// 网络错误
const handleNetworkError = (error) => {
  if (error.name === 'NetworkError') {
    ElMessage.error('网络连接失败，请检查您的网络')
  } else if (error.name === 'TimeoutError') {
    ElMessage.error('请求超时，请重试')
  } else {
    ElMessage.error('操作失败，请重试')
  }
}

// API错误
const handleAPIError = (response) => {
  switch (response.status) {
    case 401:
      ElMessage.error('请重新登录')
      // 跳转到登录页
      break
    case 403:
      ElMessage.error('权限不足')
      break
    case 422:
      ElMessage.error('提交的数据有误')
      break
    case 500:
      ElMessage.error('服务器错误，请稍后重试')
      break
    default:
      ElMessage.error('操作失败')
  }
}
```

## 安全考虑

### 1. 文件上传安全
- 限制文件类型（仅图片）
- 限制文件大小（10MB）
- 文件名重命名
- 病毒扫描（服务端）

### 2. 交易哈希验证
```javascript
const validateTransactionHash = (hash) => {
  // TRC20交易哈希为64位十六进制
  const hashRegex = /^[0-9a-fA-F]{64}$/
  return hashRegex.test(hash)
}
```

### 3. 金额验证
```javascript
const validateAmount = (amount) => {
  const num = parseFloat(amount)
  return !isNaN(num) && num > 0 && num >= 10
}
```

## 移动端适配

组件已包含响应式设计：

```scss
@media (max-width: 768px) {
  .deposit-container {
    padding: 16px;
  }
  
  .quick-buttons .el-button {
    flex: 1;
    min-width: calc(50% - 4px);
  }
  
  .step-actions {
    flex-direction: column;
    .el-button {
      width: 100%;
    }
  }
}
```

## 国际化支持

如需支持多语言：

```javascript
// i18n/zh-CN.js
export default {
  deposit: {
    title: 'USDT充值',
    steps: {
      amount: '选择金额',
      payment: '支付信息',
      proof: '提交凭证',
      complete: '完成'
    },
    amount: {
      label: '充值金额 (USDT)',
      placeholder: '请输入充值金额',
      quickSelect: '快捷选择：',
      minAmount: '最低充值金额为10 USDT'
    }
    // ... 更多文本
  }
}

// 在组件中使用
import { useI18n } from 'vue-i18n'
const { t } = useI18n()
```

## 测试

### 单元测试示例

```javascript
import { mount } from '@vue/test-utils'
import Deposit from '@/view/deposit/deposit.vue'

describe('Deposit Component', () => {
  test('should validate amount correctly', () => {
    const wrapper = mount(Deposit)
    const vm = wrapper.vm
    
    // 测试无效金额
    vm.depositAmount = '5'
    vm.validateAmount()
    expect(vm.amountError).toBe('最低充值金额为10 USDT')
    
    // 测试有效金额
    vm.depositAmount = '100'
    vm.validateAmount()
    expect(vm.amountError).toBe('')
  })
})
```

## 故障排除

### 常见问题

1. **二维码不显示**
   - 检查qrcode库是否正确安装
   - 确保DOM元素已正确挂载

2. **文件上传失败**
   - 检查文件格式和大小
   - 确认服务端接口支持multipart/form-data

3. **样式显示异常**
   - 确认Element Plus正确导入
   - 检查SCSS编译是否正常

4. **API调用失败**
   - 检查接口地址是否正确
   - 确认认证token是否有效

### 调试技巧

```javascript
// 开启调试模式
const DEBUG = process.env.NODE_ENV === 'development'

const debugLog = (message, data) => {
  if (DEBUG) {
    console.log(`[Deposit Debug] ${message}`, data)
  }
}

// 在关键位置添加调试日志
debugLog('Amount validation', { amount: depositAmount.value, error: amountError.value })
debugLog('File upload', { file: uploadedFile.value })
debugLog('API request', { url, data })
```

## 版本更新

### v1.0.0
- 初始版本
- 四步充值流程
- 基础验证功能

### 后续规划
- [ ] 添加充值进度查询
- [ ] 支持多种加密货币
- [ ] 添加充值历史页面
- [ ] 优化移动端体验

## 许可证

MIT License