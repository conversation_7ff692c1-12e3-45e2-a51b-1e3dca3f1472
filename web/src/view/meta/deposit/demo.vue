<template>
  <div class="demo-container">
    <div class="demo-header">
      <h1>USDT充值向导演示</h1>
      <p>这是一个完整的USDT充值流程演示页面</p>
    </div>

    <!-- 充值组件 -->
    <Deposit />

    <!-- 演示说明 -->
    <div class="demo-info">
      <el-card class="info-card" shadow="never">
        <template #header>
          <h3>组件功能说明</h3>
        </template>
        <div class="feature-list">
          <div class="feature-item">
            <el-icon class="feature-icon"><Check /></el-icon>
            <div class="feature-content">
              <h4>第一步：金额选择</h4>
              <p>支持自由输入和快速选择，最低充值10 USDT</p>
            </div>
          </div>
          <div class="feature-item">
            <el-icon class="feature-icon"><Check /></el-icon>
            <div class="feature-content">
              <h4>第二步：支付信息</h4>
              <p>显示收款地址和二维码，支持一键复制地址</p>
            </div>
          </div>
          <div class="feature-item">
            <el-icon class="feature-icon"><Check /></el-icon>
            <div class="feature-content">
              <h4>第三步：凭证提交</h4>
              <p>提交交易哈希和支付截图，支持拖拽上传</p>
            </div>
          </div>
          <div class="feature-item">
            <el-icon class="feature-icon"><Check /></el-icon>
            <div class="feature-content">
              <h4>第四步：完成确认</h4>
              <p>显示订单信息和处理状态，提供后续操作</p>
            </div>
          </div>
        </div>
      </el-card>

      <el-card class="integration-card" shadow="never">
        <template #header>
          <h3>集成说明</h3>
        </template>
        <div class="integration-content">
          <h4>路由配置</h4>
          <pre><code>{
  path: '/deposit',
  name: 'Deposit',
  meta: {
    title: 'USDT充值',
    requiresAuth: true
  },
  component: () => import('@/view/deposit/deposit.vue')
}</code></pre>

          <h4>使用方式</h4>
          <pre><code>&lt;template&gt;
  &lt;Deposit /&gt;
&lt;/template&gt;

&lt;script setup&gt;
import Deposit from '@/view/deposit/deposit.vue'
&lt;/script&gt;</code></pre>

          <h4>需要对接的API</h4>
          <ul>
            <li><code>GET /api/wallet/address</code> - 获取收款地址</li>
            <li><code>POST /api/deposit/submit</code> - 提交充值申请</li>
            <li><code>GET /api/deposit/history</code> - 查询充值记录</li>
          </ul>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { Check } from '@element-plus/icons-vue'
import Deposit from './deposit.vue'

// 演示页面可以添加额外的逻辑
const showInfo = ref(true)
</script>

<style lang="scss" scoped>
.demo-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.demo-header {
  text-align: center;
  margin-bottom: 40px;

  h1 {
    font-size: 32px;
    color: #303133;
    margin-bottom: 16px;
  }

  p {
    font-size: 16px;
    color: #909399;
    margin: 0;
  }
}

.demo-info {
  margin-top: 60px;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;

  .info-card, .integration-card {
    h3 {
      margin: 0;
      color: #303133;
    }
  }

  .feature-list {
    .feature-item {
      display: flex;
      align-items: flex-start;
      margin-bottom: 20px;

      &:last-child {
        margin-bottom: 0;
      }

      .feature-icon {
        color: #67c23a;
        font-size: 20px;
        margin-right: 12px;
        margin-top: 2px;
        flex-shrink: 0;
      }

      .feature-content {
        h4 {
          margin: 0 0 4px 0;
          color: #303133;
          font-size: 16px;
        }

        p {
          margin: 0;
          color: #606266;
          font-size: 14px;
          line-height: 1.5;
        }
      }
    }
  }

  .integration-content {
    h4 {
      color: #303133;
      margin: 20px 0 8px 0;
      font-size: 16px;

      &:first-child {
        margin-top: 0;
      }
    }

    pre {
      background: #f5f7fa;
      border-radius: 4px;
      padding: 16px;
      margin: 8px 0 16px 0;
      overflow-x: auto;

      code {
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        font-size: 13px;
        color: #303133;
      }
    }

    ul {
      margin: 8px 0;
      padding-left: 20px;

      li {
        margin: 4px 0;
        color: #606266;

        code {
          background: #f5f7fa;
          padding: 2px 4px;
          border-radius: 2px;
          font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
          font-size: 12px;
          color: #e6a23c;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .demo-container {
    padding: 16px;
  }

  .demo-header {
    h1 {
      font-size: 24px;
    }
  }

  .demo-info {
    grid-template-columns: 1fr;
    gap: 16px;
    margin-top: 40px;
  }

  .integration-content {
    pre {
      font-size: 12px;
      padding: 12px;
    }
  }
}
</style>
