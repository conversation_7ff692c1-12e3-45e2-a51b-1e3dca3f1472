# USDT充值向导组件

这是一个完整的USDT充值向导页面，包含四个步骤的完整流程。

## 功能特性

### 第一步：充值金额选择
- 用户可以自由输入USDT充值金额
- 提供快速选择按钮（10、50、100、200、500、1000 USDT）
- 最低充值金额验证（10 USDT）
- 充值说明和费用信息

### 第二步：支付信息展示
- 显示充值详情（金额、网络类型、预计到账时间）
- 收款地址显示和复制功能
- 自动生成收款地址二维码
- 重要提示和注意事项
- 30分钟支付倒计时

### 第三步：支付凭证提交
- 交易哈希输入
- 支付截图上传（支持拖拽）
- 备注信息（可选）
- 文件格式和大小验证

### 第四步：完成确认
- 成功提示页面
- 订单信息展示
- 处理时间说明
- 后续操作引导

## 技术特点

- 使用Vue 3 Composition API
- Element Plus UI组件库
- 响应式设计，支持移动端
- QR码自动生成
- 文件上传处理
- 表单验证
- 状态管理

## 路由配置

在 `router/index.js` 中添加路由：

```javascript
{
  path: '/deposit',
  name: 'Deposit',
  meta: {
    title: 'USDT充值',
    requiresAuth: true
  },
  component: () => import('@/view/deposit/deposit.vue')
}
```

## API集成

组件中包含以下需要对接的API端点：

### 1. 获取收款地址
```javascript
// 建议在第一步到第二步时调用
const getWalletAddress = async () => {
  // 调用后端API获取新的收款地址
  // 返回格式: { address: 'TQn9Y2khEsLJW1ChVWFMSMeRDow5KcbLSE', network: 'TRC20' }
}
```

### 2. 提交充值申请
```javascript
// 在第三步提交时调用
const submitDepositRequest = async (data) => {
  // data: {
  //   amount: '100',
  //   walletAddress: 'TQn9Y2khEsLJW1ChVWFMSMeRDow5KcbLSE',
  //   transactionHash: 'xxxxx',
  //   screenshot: File,
  //   remark: 'optional'
  // }
}
```

### 3. 查询充值记录
```javascript
// 在完成页面"查看充值记录"时调用
const getDepositHistory = async () => {
  // 获取用户充值历史记录
}
```

## 自定义配置

### 快速金额按钮
```javascript
const quickAmounts = ref([10, 50, 100, 200, 500, 1000])
// 可以根据需要修改快速选择的金额
```

### 收款地址
```javascript
const walletAddress = ref('TQn9Y2khEsLJW1ChVWFMSMeRDow5KcbLSE')
// 建议通过API动态获取，而不是硬编码
```

### 最低充值金额
```javascript
// 在validateAmount方法中修改
else if (amount < 10) {
  amountError.value = '最低充值金额为10 USDT'
}
```

## 样式自定义

组件使用SCSS编写，支持以下自定义：

- 主色调：修改CSS变量或Element Plus主题
- 布局宽度：调整`.deposit-container`的`max-width`
- 移动端适配：已包含响应式断点

## 安全考虑

1. **文件上传验证**：已包含文件类型和大小验证
2. **输入验证**：包含金额和哈希格式验证
3. **XSS防护**：使用v-model绑定，Element Plus内置防护
4. **地址验证**：建议添加钱包地址格式验证

## 使用示例

```vue
<template>
  <Deposit />
</template>

<script setup>
import Deposit from '@/view/deposit/deposit.vue'
</script>
```

## 依赖项

确保以下依赖已安装：

- `vue`: ^3.5.7
- `element-plus`: ^2.8.5
- `@element-plus/icons-vue`: ^2.3.1
- `qrcode`: ^1.5.4（已在项目中安装）

## 国际化支持

如需支持多语言，可以将硬编码的中文文本提取到语言包中：

```javascript
// i18n/zh-CN.js
export default {
  deposit: {
    title: 'USDT充值',
    selectAmount: '选择金额',
    paymentInfo: '支付信息',
    submitProof: '提交凭证',
    complete: '完成'
  }
}
```

## 浏览器兼容性

- Chrome 80+
- Firefox 75+
- Safari 13+
- Edge 80+

支持现代浏览器的ES6+特性和CSS Grid/Flexbox布局。