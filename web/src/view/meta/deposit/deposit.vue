<template>
  <div class="deposit-container">
    <!-- 步骤指示器 -->
    <div class="steps-container">
      <el-steps :active="currentStep" finish-status="success" align-center>
        <el-step title="选择金额" description="输入充值金额"></el-step>
        <el-step title="支付信息" description="查看收款地址"></el-step>
        <el-step title="提交凭证" description="上传支付凭证"></el-step>
        <el-step title="完成" description="充值完成"></el-step>
      </el-steps>
    </div>

    <!-- 步骤内容 -->
    <div class="content-container">
      <!-- 第一步：选择金额 -->
      <div v-if="currentStep === 0" class="step-content">
        <el-card class="step-card" shadow="never">
          <template #header>
            <div class="card-header">
              <h2>USDT 充值</h2>
              <p class="card-subtitle">请选择您要充值的金额</p>
            </div>
          </template>

          <div class="amount-input-section">
            <div class="input-group">
              <label class="input-label">充值金额 (USDT)</label>
              <el-input
                v-model="depositAmount"
                type="number"
                placeholder="请输入充值金额"
                size="large"
                class="amount-input"
                @input="validateAmount"
              >
                <template #prefix>
                  <span class="currency-symbol">USDT</span>
                </template>
              </el-input>
              <div v-if="amountError" class="error-message">
                {{ amountError }}
              </div>
            </div>

            <!-- 快捷金额选择 -->
            <div class="quick-amounts">
              <div class="quick-label">快捷选择：</div>
              <div class="quick-buttons">
                <el-button
                  v-for="amount in quickAmounts"
                  :key="amount"
                  :class="{ selected: depositAmount == amount }"
                  @click="setQuickAmount(amount)"
                >
                  {{ amount }}U
                </el-button>
              </div>
            </div>

            <!-- 费用说明 -->
            <div class="fee-info">
              <el-alert
                title="温馨提示"
                type="info"
                show-icon
                :closable="false"
              >
                <template #default>
                  <p>• 到账时间：0~3个工作日</p>
                </template>
              </el-alert>
            </div>
          </div>

          <div class="step-actions">
            <el-button
              type="primary"
              size="large"
              @click="nextStep"
              :disabled="!isAmountValid"
            >
              下一步
            </el-button>
          </div>
        </el-card>
      </div>

      <!-- 第二步：支付信息 -->
      <div v-if="currentStep === 1" class="step-content">
        <el-card class="step-card" shadow="never">
          <template #header>
            <div class="card-header">
              <h2>支付信息</h2>
              <p class="card-subtitle">
                请向以下地址转账 {{ depositAmount }} USDT
              </p>
            </div>
          </template>

          <div class="payment-info">
            <!-- 充值详情 -->
            <div class="deposit-details">
              <div class="detail-row">
                <span class="label">充值金额：</span>
                <span class="value">{{ depositAmount }} USDT</span>
              </div>
              <div class="detail-row">
                <span class="label">网络：</span>
                <span class="value">TRC20</span>
              </div>
              <div class="detail-row">
                <span class="label">预计到账：</span>
                <span class="value">0~3个工作日</span>
              </div>
            </div>

            <el-divider />

            <!-- 收款地址 -->
            <div class="wallet-info">
              <div class="wallet-address-section">
                <label class="wallet-label">收款地址</label>
                <div class="address-container">
                  <el-input
                    v-model="walletAddress"
                    readonly
                    size="large"
                    class="address-input"
                  >
                    <template #append>
                      <el-button @click="copyAddress">
                        <el-icon><CopyDocument /></el-icon>
                        复制
                      </el-button>
                    </template>
                  </el-input>
                </div>
              </div>

              <!-- 二维码 -->
              <div class="qr-code-section">
                <div class="qr-code-container">
                  <div class="qr-code" ref="qrCode"></div>
                  <p class="qr-tip">扫描二维码转账</p>
                </div>
              </div>
            </div>

            <!-- 重要提示 -->
            <el-alert
              title="重要提示"
              type="warning"
              show-icon
              :closable="false"
              class="warning-alert"
            >
              <template #default>
                <p>• 请在30分钟内完成转账</p>
                <p>• 转账完成后上传截图</p>
              </template>
            </el-alert>
          </div>

          <div class="step-actions">
            <el-button size="large" @click="prevStep">上一步</el-button>
            <el-button type="primary" size="large" @click="nextStep">
              我已付款
            </el-button>
          </div>
        </el-card>
      </div>

      <!-- 第三步：提交凭证 -->
      <div v-if="currentStep === 2" class="step-content">
        <el-card class="step-card" shadow="never">
          <template #header>
            <div class="card-header">
              <h2>提交支付凭证</h2>
              <p class="card-subtitle">请提交您的转账凭证以便我们尽快处理</p>
            </div>
          </template>

          <div class="proof-submission">
            <!-- 交易哈希 -->
            <div class="form-group">
              <label class="form-label">交易哈希 (Transaction Hash) *</label>
              <el-input
                v-model="transactionHash"
                placeholder="请输入交易哈希"
                size="large"
                class="form-input"
              />
              <div class="form-tip">
                交易哈希是区块链上唯一的交易标识符，通常在钱包或交易所的转账记录中可以找到
              </div>
            </div>

            <!-- 截图上传 -->
            <div class="form-group">
              <label class="form-label">支付截图 *</label>
              <el-upload
                class="screenshot-upload"
                drag
                action="#"
                :auto-upload="false"
                :on-change="handleFileChange"
                :before-upload="beforeUpload"
                accept="image/*"
                :show-file-list="false"
              >
                <div v-if="!uploadedFile" class="upload-placeholder">
                  <el-icon class="upload-icon"><UploadFilled /></el-icon>
                  <div class="upload-text">
                    <p>点击或拖拽上传支付截图</p>
                    <p class="upload-hint">
                      支持 JPG、PNG 格式，文件大小不超过 10MB
                    </p>
                  </div>
                </div>
                <div v-else class="upload-success">
                  <el-icon class="success-icon"><Check /></el-icon>
                  <p>{{ uploadedFile.name }}</p>
                  <el-button type="text" @click="removeFile"
                    >重新上传</el-button
                  >
                </div>
              </el-upload>
            </div>

            <!-- 备注 -->
            <div class="form-group">
              <label class="form-label">备注 (可选)</label>
              <el-input
                v-model="remark"
                type="textarea"
                :rows="3"
                placeholder="您可以在此处添加任何额外信息"
                size="large"
              />
            </div>
          </div>

          <div class="step-actions">
            <el-button size="large" @click="prevStep">上一步</el-button>
            <el-button
              type="primary"
              size="large"
              @click="submitProof"
              :disabled="!isProofValid"
              :loading="submitting"
            >
              提交凭证
            </el-button>
          </div>
        </el-card>
      </div>

      <!-- 第四步：完成 -->
      <div v-if="currentStep === 3" class="step-content">
        <el-card class="step-card success-card" shadow="never">
          <div class="success-content">
            <div class="success-icon">
              <el-icon><CircleCheck /></el-icon>
            </div>
            <h2 class="success-title">充值申请已提交</h2>
            <p class="success-subtitle">我们已收到您的充值申请，正在处理中</p>

            <div class="order-info">
              <div class="order-row">
                <span class="label">订单号：</span>
                <span class="value">{{ orderNumber }}</span>
              </div>
              <div class="order-row">
                <span class="label">充值金额：</span>
                <span class="value">{{ depositAmount }} USDT</span>
              </div>
              <div class="order-row">
                <span class="label">提交时间：</span>
                <span class="value">{{ submitTime }}</span>
              </div>
            </div>

            <el-alert
              title="处理说明"
              type="info"
              show-icon
              :closable="false"
              class="process-info"
            >
              <template #default>
                <p>• 我们将在1-24小时内处理您的充值申请</p>
                <p>• 处理完成后，USDT将自动到账</p>
                <p>• 您可以在"充值记录"中查看处理进度</p>
              </template>
            </el-alert>

            <div class="final-actions">
              <el-button size="large" @click="viewHistory"
                >查看充值记录</el-button
              >
              <el-button type="primary" size="large" @click="newDeposit">
                继续充值
              </el-button>
            </div>
          </div>
        </el-card>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { ref, computed, onMounted, nextTick } from 'vue'
  import { ElMessage, ElMessageBox } from 'element-plus'
  import {
    CopyDocument,
    UploadFilled,
    Check,
    CircleCheck
  } from '@element-plus/icons-vue'
  import QRCode from 'qrcode'

  // 步骤控制
  const currentStep = ref(0)

  // 第一步：金额选择
  const depositAmount = ref('')
  const amountError = ref('')
  const quickAmounts = ref([5000, 10000, 30000, 50000, 100000, 200000, 300000])

  // 第二步：支付信息
  const walletAddress = ref('TQn9Y2khEsLJW1ChVWFMSMeRDow5KcbLSE')
  const qrCode = ref(null)

  // 第三步：凭证提交
  const transactionHash = ref('')
  const uploadedFile = ref(null)
  const remark = ref('')
  const submitting = ref(false)

  // 第四步：完成信息
  const orderNumber = ref('')
  const submitTime = ref('')

  // 计算属性
  const isAmountValid = computed(() => {
    const amount = parseFloat(depositAmount.value)
    return amount >= 10 && !amountError.value
  })

  const isProofValid = computed(() => {
    return transactionHash.value.trim() !== '' && uploadedFile.value
  })

  // 方法
  const validateAmount = () => {
    const amount = parseFloat(depositAmount.value)
    if (isNaN(amount) || amount <= 0) {
      amountError.value = '请输入有效金额'
    } else if (amount < 10) {
      amountError.value = '最低充值金额为10 USDT'
    } else {
      amountError.value = ''
    }
  }

  const setQuickAmount = (amount) => {
    depositAmount.value = amount.toString()
    validateAmount()
  }

  const nextStep = () => {
    if (currentStep.value === 0 && !isAmountValid.value) {
      ElMessage.error('请输入有效的充值金额')
      return
    }

    if (currentStep.value === 2 && !isProofValid.value) {
      ElMessage.error('请填写完整的支付凭证信息')
      return
    }

    if (currentStep.value < 3) {
      currentStep.value++

      // 生成二维码
      if (currentStep.value === 1) {
        generateQRCode()
      }

      // 模拟提交处理
      if (currentStep.value === 3) {
        generateOrderInfo()
      }
    }
  }

  const prevStep = () => {
    if (currentStep.value > 0) {
      currentStep.value--
    }
  }

  const generateQRCode = async () => {
    await nextTick()
    if (qrCode.value) {
      try {
        const canvas = document.createElement('canvas')
        await QRCode.toCanvas(canvas, walletAddress.value, {
          width: 200,
          margin: 2,
          color: {
            dark: '#000000',
            light: '#FFFFFF'
          }
        })
        qrCode.value.innerHTML = ''
        qrCode.value.appendChild(canvas)
      } catch (error) {
        console.error('生成二维码失败:', error)
      }
    }
  }

  const copyAddress = async () => {
    try {
      await navigator.clipboard.writeText(walletAddress.value)
      ElMessage.success('地址已复制到剪贴板')
    } catch (error) {
      ElMessage.error('复制失败，请手动复制')
    }
  }

  const handleFileChange = (file) => {
    uploadedFile.value = file
  }

  const beforeUpload = (file) => {
    const isImage = file.type.startsWith('image/')
    const isLt10M = file.size / 1024 / 1024 < 10

    if (!isImage) {
      ElMessage.error('只能上传图片文件!')
      return false
    }
    if (!isLt10M) {
      ElMessage.error('图片大小不能超过 10MB!')
      return false
    }
    return false // 阻止自动上传
  }

  const removeFile = () => {
    uploadedFile.value = null
  }

  const submitProof = async () => {
    if (!isProofValid.value) {
      ElMessage.error('请填写完整的支付凭证信息')
      return
    }

    submitting.value = true

    try {
      // 模拟API请求
      await new Promise((resolve) => setTimeout(resolve, 2000))

      nextStep()
      ElMessage.success('凭证提交成功')
    } catch (error) {
      ElMessage.error('提交失败，请重试')
    } finally {
      submitting.value = false
    }
  }

  const generateOrderInfo = () => {
    // 生成订单号
    orderNumber.value = 'DP' + Date.now().toString().slice(-8)

    // 生成提交时间
    const now = new Date()
    submitTime.value = now.toLocaleString('zh-CN')
  }

  const viewHistory = () => {
    ElMessage.info('跳转到充值记录页面')
    // 在实际项目中，这里应该跳转到充值记录页面
    // this.$router.push('/deposit-history')
  }

  const newDeposit = () => {
    // 重置所有状态
    currentStep.value = 0
    depositAmount.value = ''
    amountError.value = ''
    transactionHash.value = ''
    uploadedFile.value = null
    remark.value = ''
    orderNumber.value = ''
    submitTime.value = ''
  }

  onMounted(() => {
    // 初始化页面
  })
</script>

<style lang="scss" scoped>
  .deposit-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
  }

  .steps-container {
    margin-bottom: 30px;

    .el-steps {
      max-width: 600px;
      margin: 0 auto;
    }
  }

  .content-container {
    min-height: 600px;
  }

  .step-content {
    display: flex;
    justify-content: center;
  }

  .step-card {
    width: 100%;
    max-width: 600px;

    .card-header {
      text-align: center;

      h2 {
        margin: 0 0 8px 0;
        font-size: 24px;
        color: #303133;
      }

      .card-subtitle {
        margin: 0;
        color: #909399;
        font-size: 14px;
      }
    }
  }

  // 第一步样式
  .amount-input-section {
    .input-group {
      margin-bottom: 24px;

      .input-label {
        display: block;
        margin-bottom: 8px;
        font-weight: 500;
        color: #303133;
      }

      .amount-input {
        .currency-symbol {
          color: #909399;
          font-weight: 500;
        }
      }

      .error-message {
        color: #f56c6c;
        font-size: 12px;
        margin-top: 4px;
      }
    }

    .quick-amounts {
      margin-bottom: 24px;

      .quick-label {
        display: block;
        margin-bottom: 12px;
        font-weight: 500;
        color: #303133;
      }

      .quick-buttons {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;

        .el-button {
          &.selected {
            background-color: #409eff;
            color: white;
          }
        }
      }
    }

    .fee-info {
      margin-bottom: 24px;

      .el-alert {
        p {
          margin: 4px 0;
        }
      }
    }
  }

  // 第二步样式
  .payment-info {
    .deposit-details {
      margin-bottom: 20px;

      .detail-row {
        display: flex;
        justify-content: space-between;
        margin-bottom: 12px;

        .label {
          color: #909399;
        }

        .value {
          font-weight: 500;
          color: #303133;
        }
      }
    }

    .wallet-info {
      .wallet-address-section {
        margin-bottom: 24px;

        .wallet-label {
          display: block;
          margin-bottom: 8px;
          font-weight: 500;
          color: #303133;
        }

        .address-container {
          .address-input {
            font-family: monospace;
          }
        }
      }

      .qr-code-section {
        text-align: center;
        margin-bottom: 24px;

        .qr-code-container {
          display: inline-block;
          padding: 20px;
          border: 1px solid #dcdfe6;
          border-radius: 8px;
          background: white;

          .qr-code {
            margin-bottom: 8px;
          }

          .qr-tip {
            margin: 0;
            color: #909399;
            font-size: 12px;
          }
        }
      }
    }

    .warning-alert {
      margin-top: 24px;

      p {
        margin: 4px 0;
      }
    }
  }

  // 第三步样式
  .proof-submission {
    .form-group {
      margin-bottom: 24px;

      .form-label {
        display: block;
        margin-bottom: 8px;
        font-weight: 500;
        color: #303133;
      }

      .form-input {
        width: 100%;
      }

      .form-tip {
        color: #909399;
        font-size: 12px;
        margin-top: 4px;
        line-height: 1.4;
      }
    }

    .screenshot-upload {
      .upload-placeholder {
        text-align: center;
        padding: 40px 20px;

        .upload-icon {
          font-size: 48px;
          color: #c0c4cc;
          margin-bottom: 16px;
        }

        .upload-text {
          p {
            margin: 4px 0;
          }

          .upload-hint {
            color: #909399;
            font-size: 12px;
          }
        }
      }

      .upload-success {
        text-align: center;
        padding: 40px 20px;

        .success-icon {
          font-size: 48px;
          color: #67c23a;
          margin-bottom: 16px;
        }

        p {
          margin: 8px 0;
          color: #303133;
        }
      }
    }
  }

  // 第四步样式
  .success-card {
    .success-content {
      text-align: center;
      padding: 20px;

      .success-icon {
        font-size: 64px;
        color: #67c23a;
        margin-bottom: 20px;
      }

      .success-title {
        margin: 0 0 8px 0;
        font-size: 24px;
        color: #303133;
      }

      .success-subtitle {
        margin: 0 0 32px 0;
        color: #909399;
      }

      .order-info {
        background: #f5f7fa;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 24px;

        .order-row {
          display: flex;
          justify-content: space-between;
          margin-bottom: 12px;

          &:last-child {
            margin-bottom: 0;
          }

          .label {
            color: #909399;
          }

          .value {
            font-weight: 500;
            color: #303133;
          }
        }
      }

      .process-info {
        margin-bottom: 32px;
        text-align: left;

        p {
          margin: 4px 0;
        }
      }

      .final-actions {
        .el-button {
          margin: 0 8px;
        }
      }
    }
  }

  // 步骤操作按钮
  .step-actions {
    display: flex;
    justify-content: center;
    gap: 16px;
    margin-top: 32px;
    padding-top: 24px;
    border-top: 1px solid #ebeef5;
  }

  // 响应式设计
  @media (max-width: 768px) {
    .deposit-container {
      padding: 16px;
    }

    .step-card {
      max-width: 100%;
    }

    .quick-buttons {
      .el-button {
        flex: 1;
        min-width: calc(50% - 4px);
      }
    }

    .step-actions {
      flex-direction: column;

      .el-button {
        width: 100%;
      }
    }

    .final-actions {
      .el-button {
        width: 100%;
        margin: 8px 0 !important;
      }
    }
  }
</style>
