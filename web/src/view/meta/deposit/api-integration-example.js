// API集成示例文件
// 这个文件展示了如何将真实的API调用集成到USDT充值组件中

import axios from 'axios'

// API基础配置
const API_BASE_URL = '/api'

// 创建axios实例
const apiClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  }
})

// 请求拦截器 - 添加认证token
apiClient.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器 - 统一错误处理
apiClient.interceptors.response.use(
  (response) => {
    return response.data
  },
  (error) => {
    console.error('API Error:', error)
    throw error
  }
)

/**
 * 充值相关API
 */
export const DepositAPI = {
  /**
   * 获取收款地址
   * @param {string} currency - 货币类型，如'USDT'
   * @param {string} network - 网络类型，如'TRC20'
   * @returns {Promise} 返回收款地址信息
   */
  async getWalletAddress(currency = 'USDT', network = 'TRC20') {
    try {
      const response = await apiClient.get('/wallet/address', {
        params: { currency, network }
      })
      return {
        success: true,
        data: {
          address: response.address,
          network: response.network,
          qrCodeUrl: response.qrCodeUrl, // 可选，如果后端已生成二维码
        }
      }
    } catch (error) {
      return {
        success: false,
        error: error.response?.data?.message || '获取收款地址失败'
      }
    }
  },

  /**
   * 提交充值申请
   * @param {Object} depositData - 充值数据
   * @returns {Promise} 返回提交结果
   */
  async submitDepositRequest(depositData) {
    try {
      // 创建FormData用于文件上传
      const formData = new FormData()
      formData.append('amount', depositData.amount)
      formData.append('walletAddress', depositData.walletAddress)
      formData.append('transactionHash', depositData.transactionHash)
      formData.append('network', depositData.network || 'TRC20')
      formData.append('currency', depositData.currency || 'USDT')

      if (depositData.screenshot) {
        formData.append('screenshot', depositData.screenshot)
      }

      if (depositData.remark) {
        formData.append('remark', depositData.remark)
      }

      const response = await apiClient.post('/deposit/submit', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      })

      return {
        success: true,
        data: {
          orderId: response.orderId,
          orderNumber: response.orderNumber,
          status: response.status,
          submitTime: response.submitTime,
          estimatedProcessTime: response.estimatedProcessTime
        }
      }
    } catch (error) {
      return {
        success: false,
        error: error.response?.data?.message || '提交充值申请失败'
      }
    }
  },

  /**
   * 查询充值记录
   * @param {Object} params - 查询参数
   * @returns {Promise} 返回充值记录列表
   */
  async getDepositHistory(params = {}) {
    try {
      const response = await apiClient.get('/deposit/history', {
        params: {
          page: params.page || 1,
          pageSize: params.pageSize || 10,
          status: params.status, // 可选：pending, processing, completed, failed
          startDate: params.startDate,
          endDate: params.endDate
        }
      })

      return {
        success: true,
        data: {
          list: response.list,
          total: response.total,
          page: response.page,
          pageSize: response.pageSize
        }
      }
    } catch (error) {
      return {
        success: false,
        error: error.response?.data?.message || '获取充值记录失败'
      }
    }
  },

  /**
   * 查询单个充值订单详情
   * @param {string} orderId - 订单ID
   * @returns {Promise} 返回订单详情
   */
  async getDepositDetail(orderId) {
    try {
      const response = await apiClient.get(`/deposit/detail/${orderId}`)
      return {
        success: true,
        data: response
      }
    } catch (error) {
      return {
        success: false,
        error: error.response?.data?.message || '获取订单详情失败'
      }
    }
  },

  /**
   * 取消充值申请
   * @param {string} orderId - 订单ID
   * @returns {Promise} 返回取消结果
   */
  async cancelDeposit(orderId) {
    try {
      const response = await apiClient.post(`/deposit/cancel/${orderId}`)
      return {
        success: true,
        data: response
      }
    } catch (error) {
      return {
        success: false,
        error: error.response?.data?.message || '取消充值失败'
      }
    }
  }
}

/**
 * 钱包相关API
 */
export const WalletAPI = {
  /**
   * 获取用户钱包余额
   * @returns {Promise} 返回钱包余额信息
   */
  async getBalance() {
    try {
      const response = await apiClient.get('/wallet/balance')
      return {
        success: true,
        data: response
      }
    } catch (error) {
      return {
        success: false,
        error: error.response?.data?.message || '获取余额失败'
      }
    }
  },

  /**
   * 获取钱包交易记录
   * @param {Object} params - 查询参数
   * @returns {Promise} 返回交易记录
   */
  async getTransactions(params = {}) {
    try {
      const response = await apiClient.get('/wallet/transactions', {
        params: {
          page: params.page || 1,
          pageSize: params.pageSize || 10,
          type: params.type, // deposit, withdraw, transfer
          startDate: params.startDate,
          endDate: params.endDate
        }
      })
      return {
        success: true,
        data: response
      }
    } catch (error) {
      return {
        success: false,
        error: error.response?.data?.message || '获取交易记录失败'
      }
    }
  }
}

/**
 * 在Vue组件中的使用示例
 */
export const useDepositAPI = () => {
  /**
   * 获取收款地址的封装方法
   */
  const fetchWalletAddress = async () => {
    const result = await DepositAPI.getWalletAddress('USDT', 'TRC20')
    if (result.success) {
      return result.data.address
    } else {
      throw new Error(result.error)
    }
  }

  /**
   * 提交充值申请的封装方法
   */
  const submitDeposit = async (formData) => {
    const result = await DepositAPI.submitDepositRequest(formData)
    if (result.success) {
      return result.data
    } else {
      throw new Error(result.error)
    }
  }

  /**
   * 获取充值历史的封装方法
   */
  const fetchDepositHistory = async (params) => {
    const result = await DepositAPI.getDepositHistory(params)
    if (result.success) {
      return result.data
    } else {
      throw new Error(result.error)
    }
  }

  return {
    fetchWalletAddress,
    submitDeposit,
    fetchDepositHistory
  }
}

/**
 * 错误处理工具函数
 */
export const handleAPIError = (error, defaultMessage = '操作失败') => {
  if (error.response) {
    // 服务器返回了错误状态码
    const status = error.response.status
    const message = error.response.data?.message || defaultMessage

    switch (status) {
      case 401:
        // 未授权，可能需要重新登录
        return '请重新登录'
      case 403:
        return '权限不足'
      case 404:
        return '请求的资源不存在'
      case 422:
        return '提交的数据有误'
      case 500:
        return '服务器内部错误'
      default:
        return message
    }
  } else if (error.request) {
    // 网络错误
    return '网络连接失败，请检查您的网络'
  } else {
    // 其他错误
    return error.message || defaultMessage
  }
}

/**
 * 文件上传前的验证
 */
export const validateUploadFile = (file) => {
  const isImage = file.type.startsWith('image/')
  const isLt10M = file.size / 1024 / 1024 < 10
  const supportedTypes = ['image/jpeg', 'image/png', 'image/jpg']

  if (!isImage) {
    return { valid: false, message: '只能上传图片文件' }
  }

  if (!supportedTypes.includes(file.type)) {
    return { valid: false, message: '只支持 JPG、PNG 格式的图片' }
  }

  if (!isLt10M) {
    return { valid: false, message: '图片大小不能超过 10MB' }
  }

  return { valid: true }
}

/**
   * 交易哈希验证
   */
export const validateTransactionHash = (hash) => {
  // TRC20交易哈希通常是64位十六进制字符串
  const hashRegex = /^[0-9a-fA-F]{64}$/

  if (!hash || hash.trim() === '') {
    return { valid: false, message: '请输入交易哈希' }
  }

  if (!hashRegex.test(hash.trim())) {
    return { valid: false, message: '交易哈希格式不正确' }
  }

  return { valid: true }
}

/**
 * USDT金额验证
 */
export const validateUSDTAmount = (amount, minAmount = 10) => {
  const numAmount = parseFloat(amount)

  if (isNaN(numAmount) || numAmount <= 0) {
    return { valid: false, message: '请输入有效的金额' }
  }

  if (numAmount < minAmount) {
    return { valid: false, message: `最低充值金额为 ${minAmount} USDT` }
  }

  // 检查小数位数（USDT通常最多6位小数）
  if (amount.toString().split('.')[1] && amount.toString().split('.')[1].length > 6) {
    return { valid: false, message: 'USDT金额最多支持6位小数' }
  }

  return { valid: true }
}
