<template>
  <div class="campaign-card" :class="{ 'selected': isSelected }" @click="handleCardClick">
    <!-- 选择框 -->
    <div class="card-selection">
      <el-checkbox 
        :model-value="isSelected"
        @change="handleSelectionChange"
        @click.stop
      />
    </div>
    
    <!-- 状态标签 -->
    <div class="card-status">
      <el-tag 
        :type="getStatusTagType(campaign.status)" 
        size="small"
        class="status-tag"
      >
        {{ getStatusText(campaign.status) }}
      </el-tag>
    </div>
    
    <!-- 卡片头部 -->
    <div class="card-header">
      <h3 class="campaign-title">
        <el-button
          type="primary"
          link
          @click.stop="handleStatisticsChart"
          class="campaign-name-link"
        >
          {{ campaign.name }}
        </el-button>
      </h3>
      <div class="campaign-dates">
        <el-icon><Calendar /></el-icon>
        <span class="date-range">
          {{ formatDate(campaign.start_time) }} - {{ formatDate(campaign.end_time) }}
        </span>
      </div>
    </div>
    
    <!-- 预算信息 -->
    <div class="budget-section">
      <div class="budget-item">
        <span class="budget-label">预算使用</span>
        <div class="budget-progress">
          <div class="budget-numbers">
            <span class="used-budget">{{ formatCurrency(campaign.used_budget || 0) }}</span>
            <span class="total-budget">/ {{ formatCurrency(campaign.total_budget) }} 元</span>
          </div>
          <el-progress 
            :percentage="getBudgetPercentage(campaign)" 
            :stroke-width="6"
            :show-text="false"
            :color="getBudgetProgressColor(campaign)"
          />
        </div>
      </div>
    </div>
    
    <!-- 投放信息 -->
    <div class="campaign-info">
      <div class="info-row">
        <div class="info-item">
          <span class="info-label">投放应用</span>
          <div class="app-tags">
            <template v-if="campaign.target_apps && campaign.target_apps.length > 0">
              <el-tag
                v-for="app in getTargetApps(campaign.target_apps)"
                :key="app"
                size="small"
                class="app-tag"
              >
                {{ app }}
              </el-tag>
            </template>
            <template v-else>
              <el-tag
                size="small"
                class="app-tag"
              >
                全部
              </el-tag>
            </template>
          </div>
        </div>
      </div>
      
      <div class="info-row">
        <div class="info-item">
          <span class="info-label">计费方式</span>
          <span class="info-value">{{ getBidTypeText(campaign.bid_type) }}</span>
        </div>
        <div class="info-item">
          <span class="info-label">出价</span>
          <span class="info-value">{{ formatCurrency(campaign.bid_amount) }} 元</span>
        </div>
      </div>
      
      <div class="info-row">
        <div class="info-item">
          <span class="info-label">日预算</span>
          <span class="info-value">{{ formatCurrency(campaign.daily_budget) }} 元</span>
        </div>
        <div class="info-item">
          <span class="info-label">广告数量</span>
          <span class="info-value">{{ getAdCount(campaign.ad_ids) }} 个广告</span>
        </div>
      </div>
    </div>
    
    <!-- 操作按钮 -->
    <div class="card-actions">
      <el-button size="small" type="primary" link @click.stop="handleView">
        <el-icon><InfoFilled /></el-icon>
        查看详情
      </el-button>
      <el-button size="small" type="primary" link @click.stop="handleEdit">
        <el-icon><Edit /></el-icon>
        编辑
      </el-button>
      <el-button v-if="campaign.status=='running'" size="small" type="primary" link @click.stop="handlePause">
        <el-icon><Stopwatch /></el-icon>
        暂停
      </el-button>
      <el-button v-if="campaign.status!='running'" size="small" type="primary" link @click.stop="handlePause">
        <el-icon><Stopwatch /></el-icon>
        恢复
      </el-button>
      <el-button size="small" type="danger" link @click.stop="handleDelete">
        <el-icon><Delete /></el-icon>
        删除
      </el-button>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { Calendar, InfoFilled, Edit, Delete, Stopwatch } from '@element-plus/icons-vue'
import { formatDate } from '@/utils/format'

// Props
const props = defineProps({
  campaign: {
    type: Object,
    required: true
  },
  isSelected: {
    type: Boolean,
    default: false
  },
  dataSource: {
    type: Object,
    default: () => ({})
  },
  billingTypeOptions: {
    type: Array,
    default: () => []
  },
  planStatusOptions: {
    type: Array,
    default: () => []
  }
})

// Emits
const emit = defineEmits(['selection-change', 'view', 'edit', 'delete', 'card-click', 'statistics-chart'])

// 处理选择变化
const handleSelectionChange = (value) => {
  emit('selection-change', props.campaign, value)
}

// 处理卡片点击
const handleCardClick = () => {
  emit('card-click', props.campaign)
}

// 处理操作
const handleView = () => {
  emit('view', props.campaign)
}

const handleEdit = () => {
  emit('edit', props.campaign)
}

const handleDelete = () => {
  emit('delete', props.campaign)
}

const handlePause = () => {
  emit('pause', props.campaign)
}

const handleStatisticsChart = () => {
  emit('statistics-chart', props.campaign)
}

// 获取状态标签类型
const getStatusTagType = (status) => {
  const statusMap = {
    'running': 'success',  // 进行中
    'paused': 'warning',  // 已暂停
    '3': 'info',     // 已结束
    '0': 'info'      // 草稿
  }
  return statusMap[status] || 'info'
}

// 获取状态文本
const getStatusText = (status) => {
  const option = props.planStatusOptions.find(item => item.value === status)
  return option ? option.label : '未知状态'
}

// 获取计费方式文本
const getBidTypeText = (bidType) => {
  const option = props.billingTypeOptions.find(item => item.value === bidType)
  return option ? option.label : '未知'
}

// 获取目标应用
const getTargetApps = (targetApps) => {
  if (!targetApps || !Array.isArray(targetApps)) return []
  if (!props.dataSource.target_apps) return targetApps
  
  return targetApps.map(appId => {
    const app = props.dataSource.target_apps.find(item => item.value === appId)
    return app ? app.label : appId
  })
}

// 获取广告数量
const getAdCount = (adIds) => {
  return Array.isArray(adIds) ? adIds.length : 0
}

// 格式化货币
const formatCurrency = (amount) => {
  if (amount === null || amount === undefined) return '0'
  return Number(amount).toLocaleString('zh-CN', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  })
}

// 获取预算使用百分比
const getBudgetPercentage = (campaign) => {
  const used = campaign.used_budget || 0
  const total = campaign.total_budget || 1
  return Math.min((used / total) * 100, 100)
}

// 获取预算进度条颜色
const getBudgetProgressColor = (campaign) => {
  const percentage = getBudgetPercentage(campaign)
  if (percentage >= 90) return '#f56c6c'
  if (percentage >= 70) return '#e6a23c'
  return '#67c23a'
}
</script>

<style scoped>
.campaign-card {
  background: white;
  border: 1px solid #e4e7ed;
  border-radius: 12px;
  padding: 20px;
  position: relative;
  transition: all 0.3s ease;
  cursor: pointer;
  overflow: hidden;
}

.campaign-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-color: var(--el-color-primary);
}

.campaign-card.selected {
  border-color: var(--el-color-primary);
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

/* 暗色主题适配 */
.dark .campaign-card {
  background: #1d1e1f;
  border-color: #414243;
}

.dark .campaign-card:hover {
  border-color: var(--el-color-primary);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

/* 选择框 */
.card-selection {
  position: absolute;
  top: 8px;
  left: 12px;
  z-index: 10;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 4px;
  padding: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
}

.dark .card-selection {
  background: rgba(29, 30, 31, 0.9);
}

.card-selection .el-checkbox {
  margin: 0;
}

.card-selection .el-checkbox__input {
  line-height: 1;
}

.card-selection .el-checkbox__inner {
  width: 16px;
  height: 16px;
}

/* 状态标签 */
.card-status {
  position: absolute;
  top: 12px;
  right: 12px;
}

.status-tag {
  font-weight: 500;
}

/* 卡片头部 */
.card-header {
  margin-top: 8px;
  margin-bottom: 16px;
}

.campaign-title {
  font-size: 18px;
  font-weight: 600;
  margin: 0 0 8px 0;
  color: #303133;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.dark .campaign-title {
  color: #e5eaf3;
}

.campaign-dates {
  display: flex;
  align-items: center;
  color: #909399;
  font-size: 13px;
}

.campaign-dates .el-icon {
  margin-right: 4px;
}

/* 预算部分 */
.budget-section {
  margin-bottom: 16px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 8px;
}

.dark .budget-section {
  background: #2a2b2c;
}

.budget-label {
  font-size: 12px;
  color: #909399;
  margin-bottom: 8px;
  display: block;
}

.budget-numbers {
  display: flex;
  align-items: baseline;
  margin-bottom: 8px;
}

.used-budget {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.dark .used-budget {
  color: #e5eaf3;
}

.total-budget {
  font-size: 14px;
  color: #909399;
  margin-left: 4px;
}

/* 信息部分 */
.campaign-info {
  margin-bottom: 16px;
}

.info-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.info-row:last-child {
  margin-bottom: 0;
}

.info-item {
  flex: 1;
  min-width: 0;
}

.info-item:not(:last-child) {
  margin-right: 12px;
}

.info-label {
  display: block;
  font-size: 12px;
  color: #909399;
  margin-bottom: 2px;
}

.info-value {
  font-size: 13px;
  color: #606266;
  font-weight: 500;
}

.dark .info-value {
  color: #a3a6ad;
}

/* 应用标签 */
.app-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  margin-top: 4px;
}

.app-tag {
  font-size: 11px;
}

/* 操作按钮 */
.card-actions {
  display: flex;
  justify-content: flex-start;
  gap: 8px;
  padding-top: 12px;
  border-top: 1px solid #f0f2f5;
}

.dark .card-actions {
  border-top-color: #414243;
}

.card-actions .el-button {
  padding: 4px 8px;
  font-size: 12px;
}

/* 计划名称链接样式 */
.campaign-name-link {
  font-size: 18px;
  font-weight: 600;
  padding: 0;
  height: auto;
  line-height: 1.2;
  text-align: left;
  color: #303133;
}

.dark .campaign-name-link {
  color: #e5eaf3;
}

.campaign-name-link:hover {
  text-decoration: underline;
  color: var(--el-color-primary);
}
</style>
