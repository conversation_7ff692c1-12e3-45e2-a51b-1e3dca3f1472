import service from '@/utils/request'

// @Tags Deposit
// @Summary 获取充值配置信息
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=object,msg=string} "获取成功"
// @Router /deposit/getConfig [get]
export const getDepositConfig = () => {
  return service({
    url: '/deposit/getConfig',
    method: 'get'
  })
}

// @Tags Deposit
// @Summary 获取钱包地址
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query string true "货币类型"
// @Success 200 {object} response.Response{data=object,msg=string} "获取成功"
// @Router /deposit/getWalletAddress [get]
export const getWalletAddress = (data) => {
  return service({
    url: '/deposit/getWalletAddress',
    method: 'get',
    params: data
  })
}

// @Tags Deposit
// @Summary 创建充值订单
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body object true "充值订单信息"
// @Success 200 {object} response.Response{data=object,msg=string} "创建成功"
// @Router /deposit/createOrder [post]
export const createDepositOrder = (data) => {
  return service({
    url: '/deposit/createOrder',
    method: 'post',
    data
  })
}

// @Tags Deposit
// @Summary 提交支付凭证
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body object true "支付凭证信息"
// @Success 200 {object} response.Response{data=object,msg=string} "提交成功"
// @Router /deposit/submitProof [post]
export const submitPaymentProof = (data) => {
  return service({
    url: '/deposit/submitProof',
    method: 'post',
    data
  })
}

// @Tags Deposit
// @Summary 上传支付截图
// @Security ApiKeyAuth
// @accept multipart/form-data
// @Produce application/json
// @Param file formData file true "支付截图文件"
// @Success 200 {object} response.Response{data=object,msg=string} "上传成功"
// @Router /deposit/uploadScreenshot [post]
export const uploadPaymentScreenshot = (file) => {
  const formData = new FormData()
  formData.append('file', file)
  return service({
    url: '/deposit/uploadScreenshot',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// @Tags Deposit
// @Summary 查询充值订单状态
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param orderNo path string true "订单号"
// @Success 200 {object} response.Response{data=object,msg=string} "查询成功"
// @Router /deposit/getOrderStatus/{orderNo} [get]
export const getDepositOrderStatus = (orderNo) => {
  return service({
    url: `/deposit/getOrderStatus/${orderNo}`,
    method: 'get'
  })
}

// @Tags Deposit
// @Summary 获取充值记录列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query object true "分页参数"
// @Success 200 {object} response.Response{data=object,msg=string} "获取成功"
// @Router /deposit/getDepositList [get]
export const getDepositList = (data) => {
  return service({
    url: '/deposit/getDepositList',
    method: 'get',
    params: data
  })
}

// @Tags Deposit
// @Summary 验证交易哈希
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body object true "交易哈希信息"
// @Success 200 {object} response.Response{data=object,msg=string} "验证成功"
// @Router /deposit/verifyTransaction [post]
export const verifyTransactionHash = (data) => {
  return service({
    url: '/deposit/verifyTransaction',
    method: 'post',
    data
  })
}

// @Tags Deposit
// @Summary 取消充值订单
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param orderNo path string true "订单号"
// @Success 200 {object} response.Response{data=object,msg=string} "取消成功"
// @Router /deposit/cancelOrder/{orderNo} [post]
export const cancelDepositOrder = (orderNo) => {
  return service({
    url: `/deposit/cancelOrder/${orderNo}`,
    method: 'post'
  })
}

// @Tags Deposit
// @Summary 获取支持的货币列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=object,msg=string} "获取成功"
// @Router /deposit/getSupportedCurrencies [get]
export const getSupportedCurrencies = () => {
  return service({
    url: '/deposit/getSupportedCurrencies',
    method: 'get'
  })
}

// @Tags Deposit
// @Summary 获取汇率信息
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param fromCurrency query string true "源货币"
// @Param toCurrency query string true "目标货币"
// @Success 200 {object} response.Response{data=object,msg=string} "获取成功"
// @Router /deposit/getExchangeRate [get]
export const getExchangeRate = (fromCurrency, toCurrency) => {
  return service({
    url: '/deposit/getExchangeRate',
    method: 'get',
    params: {
      fromCurrency,
      toCurrency
    }
  })
}
