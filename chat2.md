# 功能开发任务

完成以下开发作协


## 为移动端增加一个底部导航组件
- 有三个功能标签：首页，系统，设置；
- 首页：展示一些快捷功能和简单系统数据，如：快速添加广告，查看统计。。。等等；
- 系统：展示广告系统；
- 设置：设置页


## 增加一个广告发布向导
- 点击首页的【快速添加广告】打开此向导；
- 本向导是单页面模式，所有步骤完成后再提交数据；
- 第一步：询问用户需要达到什么效果，可选项：品牌宣传（CPM），引流（CPC）；
- 第二步：选择广告类型，广告图片等；
- 第三步：填写广告的基础数据，如：标题，描述，点击跳转URL等；
- 第四步：填写出价金额，总预算等，以及发布按钮；
- 开发新后端接口，当用户点击发布后，广告全自动发布（自动根据用户所填创建广告，取得广告ID后再自动创建投放计划）；

## 为这些页面增加大图




<!--  -->
参考截图，为 `ad.vue` 新增加一种新视图，并使用按钮切换普通/新视图，切换后需要在浏览器记住用户的选择以便下次直接使用

<!-- 2 -->
参考我提供的截图，为当前view文件新增加一种新视图，并使用按钮切换普通/新视图，切换后需要在浏览器记住用户的选择以便下次直接使用，新视图以组件方式开发，接收单个campaign实体作为参数


<!-- 3 -->
为当前view文件新增加图表视图，并使用按钮切换普通/图表，切换后需要在浏览器记住用户的选择以便下次直接使用，新视图以组件方式开发，按日期、展示、点击...维度(分多个图表，按钮切换)



图表组件需要使用真实数据

<!-- 11 -->
为 `计划名称` 添加点击打开统计图表功能，开发要求：
- 以组件方式开发，新功能需要有具备独立性和通用性，尽量少侵入原有代码，便于在其它页面中重复使用；
- 图表样式参考 `dashboard-card.vue`
- 当用户点击后，在当前页面以弹出层方式展示数据统计图表；
- 弹出层接受 id，数据类型(target_type) 作为参数，使用传入的 id 作为查询条件（`统计目标`字段）；
- target_type的类型分别是：1:APP，2:广告，3:广告计划；
- 使用多个图表展示不同维度的数据（如：展示数、点击数、日活用户数等），用tab切换不同图表，同类型的数据使用同一个图表但多条曲线展示，不同颜色区分；
- 数据获取参考：
    * 本统计图表功能是 `dashub` 的图表化实现 （参考 `web/src/view/meta/dashub/dashub.vue`）；
    * `Dashub` 是图表数据的实体，数据查询API参考 `dashub.vue`；
    * 当切换不同的图表时，使用id、target_type、图表类型等读取数据（图表类型对应的值需参考 dashub_constants.go）；
- 图表以 `统计日期` 作为横轴，根据不同的传入类型（target_type）展示多张图表：
    * 默认图表：`广告展示数`、`点击数`；
    * 当类型是广告时：展示默认图表；
    * 当类型是APP时，展示 `日活用户数`以及默认图表；
    * 当类型（target_type=3）是广告计划时：
      - 先加载广告计划自身的图表数据；
      - 再根据广告计划ID加载广告计划关联的广告ID列表；
      - 再根据广告ID列表加载广告的图表数据；
      - 把广告计划以及关联的每个广告的数据合并展示（按默认图表分类，同类型数据展示在同一个图表多条曲线展示，不同颜色区分）；



请求返回格式如下：
{"code":0,"data":{"list":[{"ID":1,"CreatedAt":"2025-06-04T14:29:12+08:00","UpdatedAt":"2025-06-04T14:29:12+08:00","day":"2025-06-04","kind":1001,"target_type":0,"target":0,"nums":5488},{"ID":2,"CreatedAt":"2025-06-04T14:29:12+08:00","UpdatedAt":"2025-06-04T14:29:12+08:00","day":"2025-06-04","kind":1002,"target_type":0,"target":0,"nums":8232},...]}}



---
为项目开发一个充值向导页面Deposit（单页面），使用USDT进行充值：
- 第一步：充值金额选择页面，用户可以自由输入金额，并有快速输入快捷金额选项（如100、200、500等）;
- 第二步：显示本次充值详情，收款地址，二维码（来自冷钱包），以及【我已付款】按钮；
- 第三步：当用户点击上一步的“我已付款”， 提交交易哈希、截图（文件选择）；
- 第四步：显示完成界面；
- 保存到 `view/deposit/deposit.vue`





---

# 开发一个服务于统计数据的集中管理类
## 目标：
    - 提供数据更新及按条件读取数据的接口；
    - 对统计数据的读取、更新实现集中管理；
    - 高频数据先更新到redis，定时更新到db（同步到 `Dashub` 对应的库表）；

## 用例：
    当广告上报的接口收到广告展示或点击数据时，以下类型的计数（点击/展示）需要按不同维度更新

    ### APP统计
        - 更新当前APP的计数（总计）；
        - 更新当前APP当天的计数（按天）；
        - 更新当前用户下全部APP汇总计数（总计）；
        - 更新当前用户下当天全部APP汇总计数（按天）；
        - 更新全平台所有APP汇总计数（总计）；
        - 更新全平台当天所有APP汇总计数（按天）；
        - 更新APP日活用户数（每个设备当天第一次请求广告计一）（按天）；
        - 更新APP用户数量（总计）；
        - 更新全平台日活用户数（按天）；
        - 更新全平台用户数量（总计）；

    ### 广告统计
        - 更新当前广告的计数（总计）；
        - 更新当前广告当天的计数（按天）；
        - 更新当前用户下全部广告汇总计数（总计）；
        - 更新当前用户下当天全部广告汇总计数（按天）；
        - 更新全平台所有广告汇总计数（总计）；
        - 更新全平台当天所有广告汇总计数（按天）；

    ### 投放计划统计
        - 更新当前投放计划的计数（总计）；
        - 更新当前投放计划当天的计数（按天）；
        - 更新当前用户下全部投放计划汇总计数（总计）；
        - 更新当前用户下当天全部投放计划汇总计数（按天）；
        - 更新全平台所有投放计划汇总计数（总计）；
        - 更新全平台当天所有投放计划汇总计数（按天）；

    ### 消费统计
        - 更新当前投放计划、广告的消费计数（总计）；
        - 更新当前投放计划、广告当天的计数（按天）；
        - 更新当前用户下全部消费汇总计数（总计）；
        - 更新当前用户下当天全部消费汇总计数（按天）；
        - 更新全平台所有消费汇总计数（总计）；
        - 更新全平台当天所有消费汇总计数（按天）；


## 示例代码：
```golang
type StatisticsManager struct {
    Uid int //当前统计数据对应的 User Id
}

func (this *StatisticsManager) UpdateAppStatistics(appId int, actionType int) error {
    var day = ...
    var UserId = this.Uid

    // 更新当前APP的计数（总计）；
    ...
    // 更新当前APP当天的计数（按天）；
    // 更新当前用户下全部APP汇总计数（总计）；
    // 更新当前用户下当天全部APP汇总计数（按天）；
    // 更新全平台所有APP汇总计数（总计）；
    // 更新全平台当天所有APP汇总计数（按天）；
    // 更新APP日活用户数（每个设备当天第一次请求广告计一）（按天）；
    // 更新APP用户数量（总计）；
    // 更新全平台日活用户数（按天）；
    // 更新全平台用户数量（总计）；
    return nil
}

func (this *StatisticsManager) SyncToDB() error {
    // 同步到数据库，参考及新增类型添加到： `server/model/meta/dashub_constants.go`
    ...
    return nil
}
```