package initialize

import (
	"context"
	"fmt"
	"github.com/flipped-aurora/gin-vue-admin/server/task"
	"github.com/robfig/cron/v3"
	"math/rand"
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
)

func Timer() {
	go func() {
		var option []cron.Option
		option = append(option, cron.WithSeconds())
		// 清理DB定时任务
		_, err := global.GVA_Timer.AddTaskByFunc("ClearDB", "@daily", func() {
			err := task.ClearTable(global.GVA_DB) // 定时任务方法定在task文件包中
			if err != nil {
				fmt.Println("timer error:", err)
			}
		}, "定时清理数据库【日志，黑名单】内容", option...)
		if err != nil {
			fmt.Println("add timer error:", err)
		}

		// 其他定时任务定在这里 参考上方使用方法

		//_, err := global.GVA_Timer.AddTaskByFunc("定时任务标识", "corn表达式", func() {
		//	具体执行内容...
		//  ......
		//}, option...)
		//if err != nil {
		//	fmt.Println("add timer error:", err)
		//}

		/*
			DASHUB_YESTERDAY_DAU    = 4004 // 昨日DAU
			DASHUB_YESTERDAY_APP    = 4005 // 昨日应用数
			DASHUB_YESTERDAY_APPNEW = 4006 // 昨日应用新增数
		*/
		_, err = global.GVA_Timer.AddTaskByFunc("update-dash4xx", "@daily", func() {
			key := "dashfake"
			v1, _ := global.GVA_REDIS.HGet(context.Background(), key, "4004").Int()
			v2, _ := global.GVA_REDIS.HGet(context.Background(), key, "4005").Int()

			//每天随机增加1~3个应用
			v3 := rand.Intn(3) + 1
			yau := int(float64(v1) * 0.1)
			app := v2 + v3

			//昨日dau 为+10% 到-10%区间随机出来一个随机的
			v5 := rand.Intn(yau*2) - yau
			dau := v1 + v5

			//appDau := 0
			appDau := rand.Intn(30000)
			if 10000 > appDau {
				appDau += 5000
			}
			dau += appDau

			//每个新增应用的日活为1~3W
			//for i := 0; i < v3; i++ {
			//	appDau = rand.Intn(30000)
			//	if 10000 > appDau {
			//		appDau += 5000
			//	}
			//	dau += appDau
			//}

			today := time.Now().Format("2006-01-02")

			_, _ = global.GVA_REDIS.HSet(context.Background(), key, "4004", dau).Result()
			_, _ = global.GVA_REDIS.HSet(context.Background(), key, "4005", app).Result()
			_, _ = global.GVA_REDIS.HSet(context.Background(), key, "4006", v3).Result()
			_, _ = global.GVA_REDIS.HSet(context.Background(), key+"_dau", today, dau).Result()

			fmt.Println("=========DAUUPDATE======> ", v1, dau)

		}, "更新dash4xx", option...)
		if err != nil {
			fmt.Println("add timer error:", err)
		}
	}()
}
