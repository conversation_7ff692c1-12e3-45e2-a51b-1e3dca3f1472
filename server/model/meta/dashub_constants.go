package meta

// Dashub统计数据类型常量定义
// 每个统计项目都有唯一的数值型ID，用于在meta_dashubs表的kind字段中标识数据类型

// 用户相关统计 (1000-1999)
const (
	DASHUB_DAILY_ACTIVE_USERS   = 1001 // 日活用户数
	DASHUB_WEEKLY_ACTIVE_USERS  = 1002 // 周活用户数
	DASHUB_MONTHLY_ACTIVE_USERS = 1003 // 月活用户数
	DASHUB_TOTAL_USERS          = 1004 // 总用户数
	DASHUB_NEW_USERS            = 1005 // 新增用户数
	DASHUB_ACTIVE_USERS         = 1006 // 活跃用户数
)

// 广告相关统计 (2000-2999)
const (
	DASHUB_AD_IMPRESSIONS       = 2001 // 广告展示数
	DASHUB_AD_CLICKS            = 2002 // 广告点击数
	DASHUB_ACTIVE_ADS           = 2003 // 投放中广告数
	DASHUB_AD_CONVERSIONS       = 2004 // 广告转化数
	DASHUB_AD_CTR               = 2005 // 广告点击率
	DASHUB_AD_CVR               = 2006 // 广告转化率
	DASHUB_AD_DAILY_IMPRESSIONS = 2007 // 日展示数
	DASHUB_AD_DAILY_CLICKS      = 2008 // 日点击数
)

// 财务相关统计 (3000-3999)
const (
	DASHUB_ACCOUNT_BALANCE = 3001 // 账户余额
	DASHUB_DAILY_CONSUME   = 3002 // 日消费
	DASHUB_WEEKLY_CONSUME  = 3003 // 周消费
	DASHUB_MONTHLY_CONSUME = 3004 // 月消费
	DASHUB_DAILY_REVENUE   = 3005 // 日收益
	DASHUB_WEEKLY_REVENUE  = 3006 // 周收益
	DASHUB_MONTHLY_REVENUE = 3007 // 月收益
	DASHUB_TOTAL_RECHARGE  = 3008 // 累计充值
	DASHUB_TOTAL_EARNING   = 3009 // 累计收入
)

// 系统相关统计 (4000-4999)
const (
	DASHUB_APP_COUNT        = 4001 // 应用数量
	DASHUB_CAMPAIGN_COUNT   = 4002 // 广告计划数量
	DASHUB_SYSTEM_STATUS    = 4003 // 系统状态
	DASHUB_YESTERDAY_DAU    = 4004 // 昨日DAU
	DASHUB_YESTERDAY_APP    = 4005 // 昨日应用数
	DASHUB_YESTERDAY_APPNEW = 4006 // 昨日应用新增数
)

const (
	DASHUB_APP_IMPRESSIONS       = 5001 // 广告展示数
	DASHUB_APP_CLICKS            = 5002 // 广告点击数
	DASHUB_APP_DAILY_IMPRESSIONS = 5003 // 日展示数
	DASHUB_APP_DAILY_CLICKS      = 5004 // 日点击数
)

// 统计项目配置结构
type DashubItemConfig struct {
	ID          int    `json:"id"`          // 统计项目ID
	Name        string `json:"name"`        // 项目名称
	Title       string `json:"title"`       // 显示标题
	Type        string `json:"type"`        // 展示类型：card, line_chart, bar_chart, pie_chart
	Unit        string `json:"unit"`        // 数值单位
	HasTrend    bool   `json:"hasTrend"`    // 是否显示趋势
	HasHistory  bool   `json:"hasHistory"`  // 是否有历史数据（用于图表）
	ColSpan     int    `json:"colSpan"`     // 占用列数（1-4）
	Description string `json:"description"` // 描述信息
	GroupID     string `json:"groupId"`     // 图表组ID，用于合并相似图表
	GroupTitle  string `json:"groupTitle"`  // 图表组标题
}

// 所有统计项目配置
var DashubItemConfigs = map[int]DashubItemConfig{
	// 用户相关统计
	DASHUB_DAILY_ACTIVE_USERS: {
		ID:          DASHUB_DAILY_ACTIVE_USERS,
		Name:        "daily_active_users",
		Title:       "日活用户",
		Type:        "multi_chart",
		Unit:        "人",
		HasTrend:    true,
		HasHistory:  true,
		ColSpan:     2,
		Description: "每日活跃用户数量趋势",
		GroupID:     "active_users",
		GroupTitle:  "活跃用户统计",
	},
	DASHUB_WEEKLY_ACTIVE_USERS: {
		ID:          DASHUB_WEEKLY_ACTIVE_USERS,
		Name:        "weekly_active_users",
		Title:       "周活用户",
		Type:        "multi_chart",
		Unit:        "人",
		HasTrend:    true,
		HasHistory:  true,
		ColSpan:     2,
		Description: "每周活跃用户数量趋势",
		GroupID:     "active_users",
		GroupTitle:  "活跃用户统计",
	},
	DASHUB_MONTHLY_ACTIVE_USERS: {
		ID:          DASHUB_MONTHLY_ACTIVE_USERS,
		Name:        "monthly_active_users",
		Title:       "月活用户",
		Type:        "multi_chart",
		Unit:        "人",
		HasTrend:    true,
		HasHistory:  true,
		ColSpan:     2,
		Description: "每月活跃用户数量趋势",
		GroupID:     "active_users",
		GroupTitle:  "活跃用户统计",
	},
	DASHUB_TOTAL_USERS: {
		ID:          DASHUB_TOTAL_USERS,
		Name:        "total_users",
		Title:       "总用户数",
		Type:        "card",
		Unit:        "人",
		HasTrend:    true,
		HasHistory:  false,
		ColSpan:     1,
		Description: "系统总用户数量",
	},
	DASHUB_NEW_USERS: {
		ID:          DASHUB_NEW_USERS,
		Name:        "new_users",
		Title:       "新增用户",
		Type:        "bar_chart",
		Unit:        "人",
		HasTrend:    true,
		HasHistory:  true,
		ColSpan:     2,
		Description: "每日新增用户数量",
	},
	DASHUB_ACTIVE_USERS: {
		ID:          DASHUB_ACTIVE_USERS,
		Name:        "active_users",
		Title:       "活跃用户",
		Type:        "card",
		Unit:        "人",
		HasTrend:    true,
		HasHistory:  false,
		ColSpan:     1,
		Description: "当前活跃用户数量",
	},

	// 广告相关统计
	DASHUB_AD_IMPRESSIONS: {
		ID:          DASHUB_AD_IMPRESSIONS,
		Name:        "ad_impressions",
		Title:       "广告展示数",
		Type:        "multi_chart",
		Unit:        "次",
		HasTrend:    true,
		HasHistory:  true,
		ColSpan:     2,
		Description: "广告展示数量趋势",
		GroupID:     "ad_performance",
		GroupTitle:  "广告效果统计",
	},
	DASHUB_AD_CLICKS: {
		ID:          DASHUB_AD_CLICKS,
		Name:        "ad_clicks",
		Title:       "广告点击数",
		Type:        "multi_chart",
		Unit:        "次",
		HasTrend:    true,
		HasHistory:  true,
		ColSpan:     2,
		Description: "广告点击数量趋势",
		GroupID:     "ad_performance",
		GroupTitle:  "广告效果统计",
	},
	DASHUB_ACTIVE_ADS: {
		ID:          DASHUB_ACTIVE_ADS,
		Name:        "active_ads",
		Title:       "投放中广告",
		Type:        "card",
		Unit:        "个",
		HasTrend:    true,
		HasHistory:  false,
		ColSpan:     1,
		Description: "当前投放中的广告数量",
	},
	DASHUB_AD_CONVERSIONS: {
		ID:          DASHUB_AD_CONVERSIONS,
		Name:        "ad_conversions",
		Title:       "广告转化数",
		Type:        "bar_chart",
		Unit:        "次",
		HasTrend:    true,
		HasHistory:  true,
		ColSpan:     2,
		Description: "广告转化数量趋势",
	},
	DASHUB_AD_CTR: {
		ID:          DASHUB_AD_CTR,
		Name:        "ad_ctr",
		Title:       "广告点击率",
		Type:        "card",
		Unit:        "%",
		HasTrend:    true,
		HasHistory:  false,
		ColSpan:     1,
		Description: "广告点击率",
	},
	DASHUB_AD_CVR: {
		ID:          DASHUB_AD_CVR,
		Name:        "ad_cvr",
		Title:       "广告转化率",
		Type:        "card",
		Unit:        "%",
		HasTrend:    true,
		HasHistory:  false,
		ColSpan:     1,
		Description: "广告转化率",
	},

	// 财务相关统计
	DASHUB_ACCOUNT_BALANCE: {
		ID:          DASHUB_ACCOUNT_BALANCE,
		Name:        "account_balance",
		Title:       "账户余额",
		Type:        "card",
		Unit:        "元",
		HasTrend:    true,
		HasHistory:  false,
		ColSpan:     1,
		Description: "当前账户余额",
	},
	DASHUB_DAILY_CONSUME: {
		ID:          DASHUB_DAILY_CONSUME,
		Name:        "daily_consume",
		Title:       "日消费",
		Type:        "multi_chart",
		Unit:        "元",
		HasTrend:    true,
		HasHistory:  true,
		ColSpan:     2,
		Description: "每日消费金额趋势",
		GroupID:     "consume_stats",
		GroupTitle:  "消费统计",
	},
	DASHUB_WEEKLY_CONSUME: {
		ID:          DASHUB_WEEKLY_CONSUME,
		Name:        "weekly_consume",
		Title:       "周消费",
		Type:        "multi_chart",
		Unit:        "元",
		HasTrend:    true,
		HasHistory:  true,
		ColSpan:     2,
		Description: "每周消费金额趋势",
		GroupID:     "consume_stats",
		GroupTitle:  "消费统计",
	},
	DASHUB_MONTHLY_CONSUME: {
		ID:          DASHUB_MONTHLY_CONSUME,
		Name:        "monthly_consume",
		Title:       "月消费",
		Type:        "multi_chart",
		Unit:        "元",
		HasTrend:    true,
		HasHistory:  true,
		ColSpan:     2,
		Description: "每月消费金额趋势",
		GroupID:     "consume_stats",
		GroupTitle:  "消费统计",
	},
	DASHUB_DAILY_REVENUE: {
		ID:          DASHUB_DAILY_REVENUE,
		Name:        "daily_revenue",
		Title:       "日收益",
		Type:        "multi_chart",
		Unit:        "元",
		HasTrend:    true,
		HasHistory:  true,
		ColSpan:     2,
		Description: "每日收益金额趋势",
		GroupID:     "revenue_stats",
		GroupTitle:  "收益统计",
	},
	DASHUB_WEEKLY_REVENUE: {
		ID:          DASHUB_WEEKLY_REVENUE,
		Name:        "weekly_revenue",
		Title:       "周收益",
		Type:        "multi_chart",
		Unit:        "元",
		HasTrend:    true,
		HasHistory:  true,
		ColSpan:     2,
		Description: "每周收益金额趋势",
		GroupID:     "revenue_stats",
		GroupTitle:  "收益统计",
	},
	DASHUB_MONTHLY_REVENUE: {
		ID:          DASHUB_MONTHLY_REVENUE,
		Name:        "monthly_revenue",
		Title:       "月收益",
		Type:        "multi_chart",
		Unit:        "元",
		HasTrend:    true,
		HasHistory:  true,
		ColSpan:     2,
		Description: "每月收益金额趋势",
		GroupID:     "revenue_stats",
		GroupTitle:  "收益统计",
	},
	DASHUB_TOTAL_RECHARGE: {
		ID:          DASHUB_TOTAL_RECHARGE,
		Name:        "total_recharge",
		Title:       "累计充值",
		Type:        "card",
		Unit:        "元",
		HasTrend:    true,
		HasHistory:  false,
		ColSpan:     1,
		Description: "累计充值金额",
	},
	DASHUB_TOTAL_EARNING: {
		ID:          DASHUB_TOTAL_EARNING,
		Name:        "total_earning",
		Title:       "累计收入",
		Type:        "card",
		Unit:        "元",
		HasTrend:    true,
		HasHistory:  false,
		ColSpan:     1,
		Description: "累计收入金额",
	},

	// 系统相关统计
	DASHUB_APP_COUNT: {
		ID:          DASHUB_APP_COUNT,
		Name:        "app_count",
		Title:       "应用数量",
		Type:        "card",
		Unit:        "个",
		HasTrend:    true,
		HasHistory:  false,
		ColSpan:     1,
		Description: "系统中的应用总数",
	},
	DASHUB_CAMPAIGN_COUNT: {
		ID:          DASHUB_CAMPAIGN_COUNT,
		Name:        "campaign_count",
		Title:       "广告计划数",
		Type:        "card",
		Unit:        "个",
		HasTrend:    true,
		HasHistory:  false,
		ColSpan:     1,
		Description: "系统中的广告计划总数",
	},
	DASHUB_SYSTEM_STATUS: {
		ID:          DASHUB_SYSTEM_STATUS,
		Name:        "system_status",
		Title:       "系统状态",
		Type:        "card",
		Unit:        "",
		HasTrend:    false,
		HasHistory:  false,
		ColSpan:     1,
		Description: "系统运行状态",
	},

	DASHUB_YESTERDAY_DAU: {
		ID:          DASHUB_YESTERDAY_DAU,
		Name:        "yesterday_dau",
		Title:       "昨日活跃用户数",
		Type:        "card",
		Unit:        "",
		HasTrend:    false,
		HasHistory:  false,
		ColSpan:     1,
		Description: "截止昨日活跃用户数",
	},

	DASHUB_YESTERDAY_APP: {
		ID:          DASHUB_YESTERDAY_APP,
		Name:        "yesterday_app",
		Title:       "全平台应用数量",
		Type:        "card",
		Unit:        "",
		HasTrend:    false,
		HasHistory:  false,
		ColSpan:     1,
		Description: "实时全平台应用数量",
	},

	DASHUB_YESTERDAY_APPNEW: {
		ID:          DASHUB_YESTERDAY_APPNEW,
		Name:        "yesterday_app_new",
		Title:       "昨日新增应用数",
		Type:        "card",
		Unit:        "",
		HasTrend:    false,
		HasHistory:  false,
		ColSpan:     1,
		Description: "昨日全平台新增应用数量",
	},
}

// 角色权限配置 - 定义每个角色可以查看的统计项目
var DashubRolePermissions = map[uint][]int{
	1000: { // 系统管理员 - 查看所有项目
		DASHUB_DAILY_ACTIVE_USERS,
		DASHUB_WEEKLY_ACTIVE_USERS,
		DASHUB_MONTHLY_ACTIVE_USERS,
		DASHUB_TOTAL_USERS,
		DASHUB_NEW_USERS,
		DASHUB_ACTIVE_USERS,
		DASHUB_AD_IMPRESSIONS,
		DASHUB_AD_CLICKS,
		DASHUB_ACTIVE_ADS,
		DASHUB_AD_CONVERSIONS,
		DASHUB_AD_CTR,
		DASHUB_AD_CVR,
		DASHUB_ACCOUNT_BALANCE,
		DASHUB_DAILY_CONSUME,
		DASHUB_WEEKLY_CONSUME,
		DASHUB_MONTHLY_CONSUME,
		DASHUB_DAILY_REVENUE,
		DASHUB_WEEKLY_REVENUE,
		DASHUB_MONTHLY_REVENUE,
		DASHUB_TOTAL_RECHARGE,
		DASHUB_TOTAL_EARNING,
		DASHUB_APP_COUNT,
		DASHUB_CAMPAIGN_COUNT,
		// DASHUB_SYSTEM_STATUS,
	},
	1001: { // 广告主 - 查看广告投放和消费相关数据
		DASHUB_YESTERDAY_DAU,
		DASHUB_YESTERDAY_APP,
		DASHUB_YESTERDAY_APPNEW,
		DASHUB_ACTIVE_ADS,
		DASHUB_AD_IMPRESSIONS,
		DASHUB_AD_CLICKS,
		DASHUB_AD_CONVERSIONS,
		DASHUB_AD_CTR,
		DASHUB_AD_CVR,
		DASHUB_ACCOUNT_BALANCE,
		DASHUB_DAILY_CONSUME,
		DASHUB_WEEKLY_CONSUME,
		DASHUB_MONTHLY_CONSUME,
		DASHUB_TOTAL_RECHARGE,
	},
	1002: { // APP开发者 - 查看广告收益和用户相关数据
		DASHUB_AD_IMPRESSIONS,
		DASHUB_AD_CLICKS,
		DASHUB_AD_CONVERSIONS,
		DASHUB_DAILY_REVENUE,
		DASHUB_WEEKLY_REVENUE,
		DASHUB_MONTHLY_REVENUE,
		DASHUB_TOTAL_EARNING,
		DASHUB_DAILY_ACTIVE_USERS,
		DASHUB_WEEKLY_ACTIVE_USERS,
		DASHUB_MONTHLY_ACTIVE_USERS,
		DASHUB_TOTAL_USERS,
		DASHUB_NEW_USERS,
		DASHUB_ACTIVE_USERS,
	},
}

// GetDashubItemConfig 获取统计项目配置
func GetDashubItemConfig(id int) (DashubItemConfig, bool) {
	config, exists := DashubItemConfigs[id]
	return config, exists
}

// GetRolePermissions 获取角色权限
func GetRolePermissions(authorityID uint) ([]int, bool) {
	if _, ok := DashubRolePermissions[888]; !ok {
		DashubRolePermissions[888] = DashubRolePermissions[1000]
	}
	permissions, exists := DashubRolePermissions[authorityID]
	return permissions, exists
}

// ChartGroup 图表组合配置
type ChartGroup struct {
	GroupID    string             `json:"groupId"`    // 组ID
	GroupTitle string             `json:"groupTitle"` // 组标题
	Items      []DashubItemConfig `json:"items"`      // 组内项目
	ColSpan    int                `json:"colSpan"`    // 占用列数
	Type       string             `json:"type"`       // 组合图表类型
}

// GetChartGroups 获取图表组合配置
func GetChartGroups(allowedKinds []int) map[string]ChartGroup {
	groups := make(map[string]ChartGroup)

	// 遍历允许的项目，按GroupID分组
	for _, kind := range allowedKinds {
		config, exists := GetDashubItemConfig(kind)
		if !exists || config.GroupID == "" {
			continue
		}

		group, exists := groups[config.GroupID]
		if !exists {
			group = ChartGroup{
				GroupID:    config.GroupID,
				GroupTitle: config.GroupTitle,
				Items:      []DashubItemConfig{},
				ColSpan:    config.ColSpan,
				Type:       "multi_chart",
			}
		}

		group.Items = append(group.Items, config)
		groups[config.GroupID] = group
	}

	return groups
}

// GetIndividualItems 获取非组合的单独项目
func GetIndividualItems(allowedKinds []int) []DashubItemConfig {
	var items []DashubItemConfig

	for _, kind := range allowedKinds {
		config, exists := GetDashubItemConfig(kind)
		if !exists || config.GroupID != "" {
			continue
		}
		items = append(items, config)
	}

	return items
}
