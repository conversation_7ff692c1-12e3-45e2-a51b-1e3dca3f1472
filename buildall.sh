#!/bin/bash

root=`pwd`

echo "Build web: 1"
echo "Build server: 2"
echo "Sync all: 4"
read -p "Choose:" a

# 检查输入是否为整数
if ! [[ "$a" =~ ^[0-9]+$ ]]; then
  echo "请输入一个整数"
  exit 1
fi


if (( (a & 2) > 0 )); then
    echo "build server"
    cd $root/server
    GOOS=linux GOARCH=amd64 go build -ldflags "-s -w" -o adserver .
fi


if (( (a & 1) > 0 )); then
    cd $root/web
    pnpm run build
fi




if (( (a & 4) > 0 )); then
    cd $root
    rss
fi


exit

read -p "build server (y/N) " -n 1 -r
echo
if [[ $REPLY == "y" ]]; then
    cd $root/server
    GOOS=linux GOARCH=amd64 go build -ldflags "-s -w" -o adserver .
fi



read -p "build web (y/N) " -n 1 -r
echo
if [[ $REPLY == "y" ]]; then
    cd $root/web
    pnpm run build
fi




read -p "sync (y/N) " -n 1 -r
echo

if [[ $REPLY == "y" ]]; then
    echo "no sync"
    rss
fi
