# 统计图表功能开发总结

## 功能概述

为广告计划管理页面添加了点击计划名称打开统计图表的功能，实现了一个独立、通用的统计图表弹出层组件。

## 开发要求完成情况

✅ **组件独立性和通用性**
- 创建了独立的 `StatisticsChartDialog.vue` 组件
- 支持通过 props 传入 id 和 target_type 参数
- 可在其他页面重复使用，代码侵入性最小

✅ **图表样式参考 dashboard-card.vue**
- 使用了相同的图表组件 (`ChartsLine`)
- 保持了一致的样式风格和主题适配
- 支持深色模式

✅ **弹出层展示**
- 使用 `el-dialog` 实现弹出层
- 响应式设计，支持移动端
- 包含加载状态和错误处理

✅ **参数支持**
- 接受 `targetId` (统计目标字段)
- 接受 `targetType` (1:APP，2:广告，3:广告计划)
- 使用传入的 id 作为查询条件

✅ **多图表展示和 Tab 切换**
- 实现了 tab 切换不同图表类型
- 默认图表：广告展示数、点击数
- APP 类型：增加日活用户数图表
- 广告计划类型：支持关联广告数据合并展示

✅ **多条曲线展示**
- 同类型数据使用同一个图表但多条曲线展示
- 不同颜色区分不同数据源
- 广告计划显示：计划数据 + 每个关联广告数据

✅ **数据获取实现**
- 参考了 `dashub.vue` 的数据查询方式
- 使用 `getDashubList` API 获取统计数据
- 支持按 `target_type`、`target`、`kind` 查询
- 图表类型对应 `dashub_constants.go` 中的常量值

## 技术实现

### 1. 组件结构

```
web/src/components/StatisticsChartDialog.vue
├── 弹出层容器 (el-dialog)
├── 加载状态处理
├── 错误状态处理
├── Tab 切换 (el-tabs)
├── 图表展示 (ChartsLine)
└── 数据处理逻辑
```

### 2. 数据流程

```
用户点击计划名称
    ↓
传入 targetId 和 targetType
    ↓
根据 targetType 配置图表类型
    ↓
并行加载数据 (广告计划 + 关联广告)
    ↓
按日期合并数据
    ↓
格式化为图表数据
    ↓
多条曲线展示
```

### 3. 关键文件修改

**新增文件：**
- `web/src/components/StatisticsChartDialog.vue` - 主组件
- `web/src/components/StatisticsChartDialog.md` - 使用文档

**修改文件：**
- `web/src/view/meta/campaign/campaign.vue` - 添加点击功能
- `web/src/view/meta/campaign/components/CampaignCard.vue` - 卡片点击功能
- `web/src/api/meta/dashub.js` - 添加图表数据API
- `server/service/meta/dashub.go` - 启用查询条件

### 4. 图表类型配置

| Target Type | 图表类型 | Kind 值 | 说明 |
|-------------|----------|---------|------|
| 1 (APP) | 日活用户数 | 1001 | DASHUB_DAILY_ACTIVE_USERS |
| 1 (APP) | 广告展示数 | 2001 | DASHUB_AD_IMPRESSIONS |
| 1 (APP) | 广告点击数 | 2002 | DASHUB_AD_CLICKS |
| 2 (广告) | 广告展示数 | 2001 | DASHUB_AD_IMPRESSIONS |
| 2 (广告) | 广告点击数 | 2002 | DASHUB_AD_CLICKS |
| 3 (广告计划) | 广告展示数 | 2001 | 计划+关联广告合并 |
| 3 (广告计划) | 广告点击数 | 2002 | 计划+关联广告合并 |

### 5. 数据合并逻辑

对于广告计划类型 (target_type=3)：
1. 获取广告计划详情，解析 `ad_ids` 字段
2. 并行加载广告计划自身数据
3. 并行加载所有关联广告数据
4. 按日期分组合并数据
5. 生成多条曲线：
   - 蓝色曲线：广告计划数据
   - 其他颜色曲线：每个关联广告数据

## 使用方式

### 在 Campaign 页面中

1. **表格视图**：点击计划名称列的链接
2. **卡片视图**：点击卡片标题中的计划名称

### 在其他页面中

```vue
<template>
  <StatisticsChartDialog
    v-model="visible"
    :target-id="targetId"
    :target-type="targetType"
  />
</template>

<script setup>
import StatisticsChartDialog from '@/components/StatisticsChartDialog.vue'

const visible = ref(false)
const targetId = ref(null)
const targetType = ref(1) // 1:APP, 2:广告, 3:广告计划

const openChart = (id, type) => {
  targetId.value = id
  targetType.value = type
  visible.value = true
}
</script>
```

## 测试建议

1. **功能测试**
   - 测试不同 target_type 的图表展示
   - 测试 tab 切换功能
   - 测试广告计划关联广告数据合并

2. **界面测试**
   - 测试响应式布局
   - 测试深色模式适配
   - 测试加载和错误状态

3. **数据测试**
   - 确保后端有相应的统计数据
   - 测试数据查询参数正确性
   - 验证图表数据格式正确

## 后续优化建议

1. **性能优化**
   - 添加数据缓存机制
   - 实现数据懒加载
   - 优化大数据量渲染

2. **功能增强**
   - 添加数据导出功能
   - 支持自定义时间范围
   - 添加更多图表类型

3. **用户体验**
   - 添加图表交互功能
   - 支持图表缩放和平移
   - 添加数据点提示信息

## 总结

本次开发成功实现了所有要求的功能，创建了一个独立、通用、易用的统计图表组件。组件具有良好的扩展性和复用性，可以方便地在其他页面中使用。通过合理的数据处理逻辑，实现了复杂的广告计划关联数据合并展示功能。
